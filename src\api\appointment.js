/**
 * 预约相关API
 */
import { post, get } from '@/utils/request';

/**
 * 创建预约
 * @param {Object} data - 预约信息
 * @returns {Promise} - 创建结果
 */
export const createAppointment = (data) => {
  return post('/appointments', data);
};

/**
 * 获取预约详情
 * @param {string} appointmentId - 预约ID
 * @returns {Promise} - 预约详情
 */
export const getAppointmentDetail = (appointmentId) => {
  if (!appointmentId) {
    return Promise.reject(new Error('未提供预约ID'));
  }
  return get(`/appointments/${appointmentId}`);
};

export default {
  createAppointment,
  getAppointmentDetail
}; 