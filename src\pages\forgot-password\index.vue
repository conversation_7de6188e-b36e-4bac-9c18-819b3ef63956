<template>
  <view class="forgot-container">
    <!-- 忘记密码卡片 -->
    <view class="forgot-card">
      <view class="forgot-header">
        <text class="title">找回密码</text>
        <text class="subtitle">请填写账号信息验证身份</text>
      </view>
      
      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step active">
          <view class="step-circle">1</view>
          <text class="step-text">验证身份</text>
        </view>
        <view class="step-line"></view>
        <view class="step" :class="{ active: currentStep >= 2 }">
          <view class="step-circle">2</view>
          <text class="step-text">重置密码</text>
        </view>
        <view class="step-line"></view>
        <view class="step" :class="{ active: currentStep >= 3 }">
          <view class="step-circle">3</view>
          <text class="step-text">完成</text>
        </view>
      </view>
      
      <!-- 步骤1：验证身份 -->
      <view v-if="currentStep === 1">
        <!-- 账号输入 -->
        <view class="form-item">
          <text class="label">账号</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入手机号或邮箱" 
              v-model="formData.account" 
              placeholder-class="placeholder"
            />
            <text class="icon">👤</text>
          </view>
          <text v-if="errors.account" class="error-tip">{{ errors.account }}</text>
        </view>
        
        <!-- 安全问题 -->
        <view class="form-item">
          <text class="label">安全问题</text>
          <text class="sub-label">您时常最想念的物品或人</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入安全问题答案" 
              v-model="formData.securityAnswer" 
              placeholder-class="placeholder"
            />
            <text class="icon">🔑</text>
          </view>
          <text v-if="errors.securityAnswer" class="error-tip">{{ errors.securityAnswer }}</text>
        </view>
        
        <button class="submit-button" @tap="verifyIdentity">下一步</button>
      </view>
      
      <!-- 步骤2：重置密码 -->
      <view v-if="currentStep === 2">
        <!-- 新密码 -->
        <view class="form-item">
          <text class="label">新密码</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              :type="showPassword ? 'text' : 'password'" 
              placeholder="请设置新密码" 
              v-model="formData.newPassword" 
              placeholder-class="placeholder"
            />
            <text class="icon clickable" @tap="togglePasswordVisibility">{{ showPassword ? '👁️' : '🔒' }}</text>
          </view>
          <text v-if="errors.newPassword" class="error-tip">{{ errors.newPassword }}</text>
        </view>
        
        <!-- 确认新密码 -->
        <view class="form-item">
          <text class="label">确认新密码</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              :type="showConfirmPassword ? 'text' : 'password'" 
              placeholder="请再次输入新密码" 
              v-model="formData.confirmPassword" 
              placeholder-class="placeholder"
            />
            <text class="icon clickable" @tap="toggleConfirmPasswordVisibility">{{ showConfirmPassword ? '👁️' : '🔒' }}</text>
          </view>
          <text v-if="errors.confirmPassword" class="error-tip">{{ errors.confirmPassword }}</text>
        </view>
        
        <button class="submit-button" @tap="resetPassword">确认修改</button>
      </view>
      
      <!-- 步骤3：完成 -->
      <view v-if="currentStep === 3" class="success-container">
        <view class="success-icon">✓</view>
        <text class="success-title">密码重置成功</text>
        <text class="success-message">您的密码已经成功重置，请使用新密码登录</text>
        <button class="submit-button" @tap="goToLogin">返回登录</button>
      </view>
      
      <!-- 返回登录 -->
      <view class="login-link" v-if="currentStep < 3">
        <text>记起密码了？</text>
        <text class="link" @tap="goToLogin">返回登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 当前步骤
const currentStep = ref(1);

// 表单数据
const formData = reactive({
  account: '',
  securityAnswer: '',
  newPassword: '',
  confirmPassword: ''
});

// 错误信息
const errors = reactive({
  account: '',
  securityAnswer: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码可见性
const showPassword = ref(false);
const showConfirmPassword = ref(false);

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 切换确认密码可见性
const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// 验证账号
const validateAccount = () => {
  const phoneReg = /^1[3-9]\d{9}$/;
  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  
  if (!formData.account) {
    errors.account = '请输入手机号或邮箱';
    return false;
  } else if (!phoneReg.test(formData.account) && !emailReg.test(formData.account)) {
    errors.account = '请输入正确的手机号或邮箱';
    return false;
  } else {
    errors.account = '';
    return true;
  }
};

// 验证安全问题
const validateSecurityAnswer = () => {
  if (!formData.securityAnswer) {
    errors.securityAnswer = '请输入安全问题答案';
    return false;
  } else {
    errors.securityAnswer = '';
    return true;
  }
};

// 验证新密码
const validateNewPassword = () => {
  if (!formData.newPassword) {
    errors.newPassword = '请设置新密码';
    return false;
  } else if (formData.newPassword.length < 6) {
    errors.newPassword = '密码长度不能少于6位';
    return false;
  } else {
    errors.newPassword = '';
    return true;
  }
};

// 验证确认密码
const validateConfirmPassword = () => {
  if (!formData.confirmPassword) {
    errors.confirmPassword = '请再次输入新密码';
    return false;
  } else if (formData.confirmPassword !== formData.newPassword) {
    errors.confirmPassword = '两次输入的密码不一致';
    return false;
  } else {
    errors.confirmPassword = '';
    return true;
  }
};

// 验证身份
const verifyIdentity = () => {
  const accountValid = validateAccount();
  const securityAnswerValid = validateSecurityAnswer();
  
  if (!accountValid || !securityAnswerValid) {
    return;
  }
  
  // 模拟验证过程
  uni.showLoading({
    title: '验证中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 这里应该是实际的验证逻辑
    // 假设验证成功
    currentStep.value = 2;
  }, 1500);
};

// 重置密码
const resetPassword = () => {
  const newPasswordValid = validateNewPassword();
  const confirmPasswordValid = validateConfirmPassword();
  
  if (!newPasswordValid || !confirmPasswordValid) {
    return;
  }
  
  // 模拟重置过程
  uni.showLoading({
    title: '提交中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 这里应该是实际的密码重置逻辑
    // 假设重置成功
    currentStep.value = 3;
  }, 1500);
};

// 返回登录页
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index'
  });
};
</script>

<style lang="scss">
.forgot-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #a8c0ff;
  padding: 30rpx;
}

.forgot-card {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.forgot-header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 44rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 0 20rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .step-circle {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    margin-bottom: 10rpx;
  }
  
  .step-text {
    font-size: 24rpx;
    color: #999;
  }
  
  &.active {
    .step-circle {
      background-color: #a8c0ff;
    }
    
    .step-text {
      color: #a8c0ff;
      font-weight: 500;
    }
  }
}

.step-line {
  flex: 1;
  height: 2rpx;
  background-color: #e0e0e0;
  margin: 0 10rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 25rpx;
  
  .label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 8rpx;
    display: block;
  }
  
  .sub-label {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    display: block;
  }
  
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    
    .input {
      flex: 1;
      height: 90rpx;
      border: 1px solid #e0e0e0;
      border-radius: 16rpx;
      padding: 0 80rpx 0 30rpx;
      font-size: 28rpx;
      background-color: #fff;
      
      &:focus {
        border-color: #a8c0ff;
      }
    }
    
    .icon {
      position: absolute;
      right: 30rpx;
      font-size: 36rpx;
      color: #999;
      
      &.clickable {
        cursor: pointer;
      }
    }
    
    .placeholder {
      color: #999;
    }
  }
  
  .error-tip {
    font-size: 24rpx;
    color: #ff4d4f;
    margin-top: 8rpx;
    display: block;
  }
}

.submit-button {
  width: 100%;
  height: 90rpx;
  background-color: #a8c0ff;
  color: #fff;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  
  &:active {
    background-color: #97b1f8;
  }
}

.login-link {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  
  .link {
    color: #8e99f3;
    margin-left: 10rpx;
  }
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  
  .success-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background-color: #a8c0ff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60rpx;
    margin-bottom: 30rpx;
  }
  
  .success-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .success-message {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 40rpx;
  }
}
</style> 