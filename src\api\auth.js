/**
 * 认证相关API
 */
import { post } from '@/utils/request';
import { saveUserInfo, clearUserInfo } from '@/utils/storage';
// 导入模拟数据（仅在开发环境使用）
import { mockLogin, mockRegister, mockLogout } from '@/mock/auth';

// 是否使用模拟数据（设置为false，尝试使用真实API）
const USE_MOCK = true;
// 如果真实API请求失败，是否自动回退到模拟数据
const FALLBACK_TO_MOCK = true;

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @returns {Promise} - 注册结果
 */
export const register = async (data) => {
  if (USE_MOCK) {
    console.log('使用模拟数据进行注册');
    return mockRegister(data);
  }
  
  try {
    // 尝试使用真实API
    console.log('尝试使用真实API进行注册');
    const result = await post('/auth/register', data, { auth: false });
    console.log('真实API注册成功:', result);
    return result;
  } catch (error) {
    console.error('真实API注册失败:', error);
    
    // 如果配置了回退到模拟数据且不是由于用户输入错误导致的失败
    if (FALLBACK_TO_MOCK && (error.code >= 500 || error.code === undefined)) {
      console.log('回退到模拟数据进行注册');
      return mockRegister(data);
    }
    
    throw error;
  }
};

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @returns {Promise} - 登录结果
 */
export const login = async (data) => {
  try {
    let result;
    
    if (USE_MOCK) {
      console.log('使用模拟数据进行登录');
      result = await mockLogin(data);
    } else {
      // 尝试使用真实API
      console.log('尝试使用真实API进行登录');
      try {
        result = await post('/auth/login', data, { auth: false });
        console.log('真实API登录成功:', result);
      } catch (error) {
        console.error('真实API登录失败:', error);
        
        // 如果配置了回退到模拟数据且不是由于用户输入错误导致的失败
        if (FALLBACK_TO_MOCK && (error.code >= 500 || error.code === undefined)) {
          console.log('回退到模拟数据进行登录');
          result = await mockLogin(data);
        } else {
          throw error;
        }
      }
    }
    
    // 登录成功，保存用户信息
    if (result && result.data) {
      // 保存Token
      uni.setStorageSync('DENTAL_HEALTH_TOKEN', result.data.token);
      
      // 保存过期时间
      if (result.data.expireTime) {
        uni.setStorageSync('TOKEN_EXPIRE_TIME', result.data.expireTime);
      }
      
      // 保存用户信息
      saveUserInfo(result.data);
    }
    
    return result;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};

/**
 * 找回密码（获取重置令牌）
 * @param {Object} data - 找回密码信息
 * @returns {Promise} - 找回密码结果
 */
export const forgotPassword = async (data) => {
  if (USE_MOCK) {
    // 模拟找回密码功能
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '验证成功',
          data: {
            resetToken: `mock_reset_token_${Date.now()}`
          }
        });
      }, 1000);
    });
  }
  
  try {
    // 尝试使用真实API
    return await post('/auth/forgot-password', data, { auth: false });
  } catch (error) {
    // 如果配置了回退到模拟数据且不是由于用户输入错误导致的失败
    if (FALLBACK_TO_MOCK && (error.code >= 500 || error.code === undefined)) {
      console.log('回退到模拟数据进行找回密码');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            message: '验证成功',
            data: {
              resetToken: `mock_reset_token_${Date.now()}`
            }
          });
        }, 1000);
      });
    }
    
    throw error;
  }
};

/**
 * 重置密码
 * @param {Object} data - 重置密码信息
 * @returns {Promise} - 重置密码结果
 */
export const resetPassword = async (data) => {
  if (USE_MOCK) {
    // 模拟重置密码功能
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '密码重置成功'
        });
      }, 1000);
    });
  }
  
  try {
    // 尝试使用真实API
    return await post('/auth/reset-password', data, { auth: false });
  } catch (error) {
    // 如果配置了回退到模拟数据且不是由于用户输入错误导致的失败
    if (FALLBACK_TO_MOCK && (error.code >= 500 || error.code === undefined)) {
      console.log('回退到模拟数据进行重置密码');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            message: '密码重置成功'
          });
        }, 1000);
      });
    }
    
    throw error;
  }
};

/**
 * 退出登录
 * @returns {Promise} - 退出登录结果
 */
export const logout = async () => {
  try {
    let result;
    
    if (USE_MOCK) {
      console.log('使用模拟数据进行退出登录');
      result = await mockLogout();
    } else {
      // 尝试使用真实API
      try {
        result = await post('/auth/logout');
      } catch (error) {
        // 如果配置了回退到模拟数据且不是由于用户输入错误导致的失败
        if (FALLBACK_TO_MOCK && (error.code >= 500 || error.code === undefined)) {
          console.log('回退到模拟数据进行退出登录');
          result = await mockLogout();
        } else {
          throw error;
        }
      }
    }
    
    // 清除本地用户信息
    clearUserInfo();
    
    return result;
  } catch (error) {
    // 即使请求失败，也清除本地用户信息
    clearUserInfo();
    throw error;
  }
};

export default {
  register,
  login,
  forgotPassword,
  resetPassword,
  logout
}; 