<template>
  <view class="container">
      <!-- 管理您的时间提示 -->
      <view class="time-management-banner">
        <text class="banner-text">管理您的时间</text>
      </view>
      
      <!-- 日期选择器 -->
      <view class="date-selector">
        <view class="month-selector">
          <text class="month-nav" @tap="prevMonth">◀</text>
          <text class="month-text">{{ currentYear }}年{{ currentMonth + 1 }}月</text>
          <text class="month-nav" @tap="nextMonth">▶</text>
        </view>
        
        <view class="week-header">
          <text v-for="(day, index) in weekDays" :key="index" class="week-day">{{ day }}</text>
        </view>
        
        <view class="calendar">
          <view 
            v-for="(day, index) in calendarDays" 
            :key="index" 
            class="calendar-day" 
            :class="{ 
              'other-month': !day.currentMonth, 
              'today': day.isToday,
              'selected': isDateSelected(day.date),
              'has-schedule': hasSchedule(day.date)
            }"
            @touchstart="startDateSelection(day)"
            @touchmove.prevent="continueDateSelection"
            @touchend="endDateSelection"
          >
            <text class="day-number">{{ day.day }}</text>
            <view v-if="hasSchedule(day.date)" class="schedule-indicator"></view>
          </view>
        </view>
      </view>
      
      <!-- 时间段设置 -->
      <view class="time-slots-container" v-if="selectedDates.length > 0">
        <view class="section-header">
          <text class="section-title">{{ formatSelectedDatesText }} 出诊时间段</text>
        </view>
        
        <!-- 上午时间段 -->
        <view class="time-period">
          <view class="period-title">上午</view>
          <view class="time-grid">
            <view 
              v-for="(slot, index) in morningTimeSlots" 
              :key="index"
              class="time-slot-item"
              :class="{ 'selected': slot.selected }"
              @tap="toggleTimeSlot(slot)"
            >
              {{ slot.time }}
            </view>
          </view>
        </view>
        
        <!-- 下午时间段 -->
        <view class="time-period">
          <view class="period-title">下午</view>
          <view class="time-grid">
            <view 
              v-for="(slot, index) in afternoonTimeSlots" 
              :key="index"
              class="time-slot-item"
              :class="{ 'selected': slot.selected }"
              @tap="toggleTimeSlot(slot)"
            >
              {{ slot.time }}
            </view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="actions">
          <button class="select-all-btn" @tap="toggleSelectAll">{{ allSelected ? '取消全选' : '全选' }}</button>
          <button class="confirm-btn" @tap="confirmSchedule">确认管理</button>
        </view>
      </view>
      
      <view class="no-date-selected" v-else>
        <text>请选择日期来设置出诊时间段</text>
      </view>
  </view>
</template>



<script setup>
import { ref, computed, onMounted } from 'vue';

// 星期几
const weekDays = ['日', '一', '二', '三', '四', '五', '六'];

// 当前日期
const today = new Date();
const currentYear = ref(today.getFullYear());
const currentMonth = ref(today.getMonth());
const selectedDates = ref([]);

// 多选日期相关
const isSelecting = ref(false);
const selectionStartDay = ref(null);

// 时间段数据
const morningTimeSlots = ref([
  { time: '08:00-08:30', selected: false },
  { time: '08:30-09:00', selected: false },
  { time: '09:00-09:30', selected: false },
  { time: '09:30-10:00', selected: false },
  { time: '10:00-10:30', selected: false },
  { time: '10:30-11:00', selected: false },
  { time: '11:00-11:30', selected: false },
  { time: '11:30-12:00', selected: false }
]);

const afternoonTimeSlots = ref([
  { time: '14:00-14:30', selected: false },
  { time: '14:30-15:00', selected: false },
  { time: '15:00-15:30', selected: false },
  { time: '15:30-16:00', selected: false },
  { time: '16:00-16:30', selected: false },
  { time: '16:30-17:00', selected: false },
  { time: '17:00-17:30', selected: false },
  { time: '17:30-18:00', selected: false }
]);

// 全选状态
const allSelected = computed(() => {
  const morning = morningTimeSlots.value.every(slot => slot.selected);
  const afternoon = afternoonTimeSlots.value.every(slot => slot.selected);
  return morning && afternoon;
});

// 医生排班数据
const scheduleData = ref([]);

// 生成日历数据
const calendarDays = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  
  // 当月第一天
  const firstDay = new Date(year, month, 1);
  // 当月最后一天
  const lastDay = new Date(year, month + 1, 0);
  
  // 上月需要显示的天数
  const prevMonthDays = firstDay.getDay();
  // 当月天数
  const daysInMonth = lastDay.getDate();
  
  const days = [];
  
  // 添加上月的日期
  const prevMonth = month === 0 ? 11 : month - 1;
  const prevMonthYear = month === 0 ? year - 1 : year;
  const prevMonthLastDay = new Date(prevMonthYear, prevMonth + 1, 0).getDate();
  
  for (let i = prevMonthDays - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i;
    days.push({
      day,
      date: new Date(prevMonthYear, prevMonth, day),
      currentMonth: false,
      isToday: false
    });
  }
  
  // 添加当月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i);
    days.push({
      day: i,
      date,
      currentMonth: true,
      isToday: isToday(date)
    });
  }
  
  // 添加下月的日期
  const nextDays = 42 - days.length; // 6行7列 = 42
  const nextMonth = month === 11 ? 0 : month + 1;
  const nextMonthYear = month === 11 ? year + 1 : year;
  
  for (let i = 1; i <= nextDays; i++) {
    days.push({
      day: i,
      date: new Date(nextMonthYear, nextMonth, i),
      currentMonth: false,
      isToday: false
    });
  }
  
  return days;
});

// 选中日期的格式化显示
const formatSelectedDatesText = computed(() => {
  if (selectedDates.value.length === 0) return '';
  if (selectedDates.value.length === 1) {
    const date = new Date(selectedDates.value[0]);
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  }
  return `已选择 ${selectedDates.value.length} 天`;
});

// 页面加载时
onMounted(() => {
  // 获取排班数据
  fetchScheduleData();
});

// 获取排班数据
const fetchScheduleData = () => {
  // 模拟数据，实际应该从API获取
  const mockData = [
    {
      date: formatDateToString(today),
      timeSlots: [
        {
          startTime: '09:00',
          endTime: '10:30',
          capacity: 4,
          booked: 2,
          notes: '普通门诊'
        },
        {
          startTime: '14:00',
          endTime: '16:00',
          capacity: 6,
          booked: 1,
          notes: '专家门诊'
        }
      ]
    },
    {
      date: formatDateToString(new Date(today.getFullYear(), today.getMonth(), today.getDate() + 2)),
      timeSlots: [
        {
          startTime: '08:30',
          endTime: '11:30',
          capacity: 5,
          booked: 0,
          notes: ''
        }
      ]
    }
  ];
  
  scheduleData.value = mockData;
};

// 检查日期是否为今天
const isToday = (date) => {
  const today = new Date();
  return date.getDate() === today.getDate() && 
         date.getMonth() === today.getMonth() && 
         date.getFullYear() === today.getFullYear();
};

// 检查日期是否被选中
const isDateSelected = (date) => {
  return selectedDates.value.some(selectedDate => {
    const selected = new Date(selectedDate);
    return date.getDate() === selected.getDate() && 
           date.getMonth() === selected.getMonth() && 
           date.getFullYear() === selected.getFullYear();
  });
};

// 检查日期是否有排班
const hasSchedule = (date) => {
  const dateStr = formatDateToString(date);
  return scheduleData.value.some(s => s.date === dateStr);
};

// 日期转字符串格式
const formatDateToString = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 上个月
const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentYear.value--;
    currentMonth.value = 11;
  } else {
    currentMonth.value--;
  }
};

// 下个月
const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentYear.value++;
    currentMonth.value = 0;
  } else {
    currentMonth.value++;
  }
};

// 开始日期选择
const startDateSelection = (day) => {
  if (!day.currentMonth) {
    // 如果选择的是其他月份的日期，切换到对应月份
    currentMonth.value = day.date.getMonth();
    currentYear.value = day.date.getFullYear();
  }
  
  isSelecting.value = true;
  selectionStartDay.value = day;
  
  // 清除之前的选择
  selectedDates.value = [day.date];
  
  // 重置时间段选择
  resetTimeSlotSelection();
  
  // 加载已有的时间段设置
  loadExistingTimeSlots();
};

// 继续日期选择（滑动）
const continueDateSelection = (event) => {
  if (!isSelecting.value) return;
  
  const touch = event.touches[0];
  const elements = document.elementsFromPoint(touch.clientX, touch.clientY);
  
  // 找到被触摸的日期元素
  const dayElement = elements.find(el => el.classList.contains('calendar-day'));
  if (dayElement) {
    const index = Array.from(dayElement.parentNode.children).indexOf(dayElement);
    if (index >= 0 && index < calendarDays.value.length) {
      const day = calendarDays.value[index];
      
      // 检查是否是当前月份的日期
      if (day.currentMonth) {
        // 如果不在已选择列表中，添加到选择列表
        if (!isDateSelected(day.date)) {
          selectedDates.value.push(day.date);
        }
      }
    }
  }
};

// 结束日期选择
const endDateSelection = () => {
  isSelecting.value = false;
  
  // 对选择的日期进行排序
  selectedDates.value.sort((a, b) => new Date(a) - new Date(b));
};

// 重置时间段选择
const resetTimeSlotSelection = () => {
  morningTimeSlots.value.forEach(slot => slot.selected = false);
  afternoonTimeSlots.value.forEach(slot => slot.selected = false);
};

// 加载已有的时间段设置
const loadExistingTimeSlots = () => {
  if (selectedDates.value.length !== 1) return;
  
  const dateStr = formatDateToString(selectedDates.value[0]);
  const schedule = scheduleData.value.find(s => s.date === dateStr);
  
  if (schedule) {
    schedule.timeSlots.forEach(slot => {
      const startHour = parseInt(slot.startTime.split(':')[0]);
      const endHour = parseInt(slot.endTime.split(':')[0]);
      
      // 根据时间段选择对应的选项
      if (startHour < 12) {
        morningTimeSlots.value.forEach(morningSlot => {
          const slotStartHour = parseInt(morningSlot.time.split('-')[0].split(':')[0]);
          const slotEndHour = parseInt(morningSlot.time.split('-')[1].split(':')[0]);
          
          if (slotStartHour >= startHour && slotEndHour <= endHour) {
            morningSlot.selected = true;
          }
        });
      } else {
        afternoonTimeSlots.value.forEach(afternoonSlot => {
          const slotStartHour = parseInt(afternoonSlot.time.split('-')[0].split(':')[0]);
          const slotEndHour = parseInt(afternoonSlot.time.split('-')[1].split(':')[0]);
          
          if (slotStartHour >= startHour && slotEndHour <= endHour) {
            afternoonSlot.selected = true;
          }
        });
      }
    });
  }
};

// 切换时间段选择
const toggleTimeSlot = (slot) => {
  slot.selected = !slot.selected;
};

// 切换全选
const toggleSelectAll = () => {
  const newState = !allSelected.value;
  morningTimeSlots.value.forEach(slot => slot.selected = newState);
  afternoonTimeSlots.value.forEach(slot => slot.selected = newState);
};

// 确认排班
const confirmSchedule = () => {
  // 获取选中的时间段
  const selectedMorningSlots = morningTimeSlots.value.filter(slot => slot.selected);
  const selectedAfternoonSlots = afternoonTimeSlots.value.filter(slot => slot.selected);
  
  if (selectedMorningSlots.length === 0 && selectedAfternoonSlots.length === 0) {
    uni.showToast({
      title: '请至少选择一个时间段',
      icon: 'none'
    });
    return;
  }
  
  // 为每个选中的日期创建排班
  selectedDates.value.forEach(date => {
    const dateStr = formatDateToString(new Date(date));
    let scheduleIndex = scheduleData.value.findIndex(s => s.date === dateStr);
    
    // 如果该日期没有排班记录，创建一个新的
    if (scheduleIndex === -1) {
      scheduleData.value.push({
        date: dateStr,
        timeSlots: []
      });
      scheduleIndex = scheduleData.value.length - 1;
    } else {
      // 清除该日期的现有时间段
      scheduleData.value[scheduleIndex].timeSlots = [];
    }
    
    // 添加选中的上午时间段
    selectedMorningSlots.forEach(slot => {
      const [startTime, endTime] = slot.time.split('-');
      scheduleData.value[scheduleIndex].timeSlots.push({
        startTime,
        endTime,
        capacity: 3,
        booked: 0,
        notes: '上午门诊'
      });
    });
    
    // 添加选中的下午时间段
    selectedAfternoonSlots.forEach(slot => {
      const [startTime, endTime] = slot.time.split('-');
      scheduleData.value[scheduleIndex].timeSlots.push({
        startTime,
        endTime,
        capacity: 3,
        booked: 0,
        notes: '下午门诊'
      });
    });
  });
  
  // 实际应用中应该调用API保存更改
  uni.showToast({
    title: '排班设置成功',
    icon: 'success'
  });
  
  // 清除选择
  selectedDates.value = [];
  resetTimeSlotSelection();
};




</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 30rpx;
}



/* 管理您的时间横幅 */
.time-management-banner {
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.banner-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
}

/* 日期选择器样式 */
.date-selector {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.month-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.month-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.month-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(142, 153, 243, 0.1);
  border-radius: 50%;
  color: #8e99f3;
  font-size: 28rpx;
}

.week-header {
  display: flex;
  margin-bottom: 20rpx;
}

.week-day {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 0;
}

.calendar {
  display: flex;
  flex-wrap: wrap;
}

.calendar-day {
  width: calc(100% / 7);
  height: 80rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 10rpx;
}

.day-number {
  font-size: 28rpx;
  color: #333;
}

.other-month .day-number {
  color: #ccc;
}

.today {
  background-color: rgba(142, 153, 243, 0.1);
  border-radius: 50%;
}

.selected {
  background-color: #8e99f3;
  border-radius: 50%;
}

.selected .day-number {
  color: #fff;
}

.has-schedule {
  position: relative;
}

.schedule-indicator {
  width: 8rpx;
  height: 8rpx;
  background-color: #ff6b6b;
  border-radius: 50%;
  position: absolute;
  bottom: 8rpx;
}

/* 时间段设置样式 */
.time-slots-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.time-period {
  margin-bottom: 30rpx;
}

.period-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 20rpx;
}

.time-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.time-slot-item {
  width: calc(50% - 10rpx);
  padding: 20rpx 0;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.time-slot-item.selected {
  background-color: #8e99f3;
  color: #fff;
}

.actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.select-all-btn {
  width: 30%;
  background-color: #f0f0f0;
  color: #333;
  font-size: 28rpx;
  padding: 20rpx 0;
  border-radius: 10rpx;
  border: none;
}

.confirm-btn {
  width: 65%;
  background-color: #8e99f3;
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 0;
  border-radius: 10rpx;
  border: none;
}

.no-date-selected {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  color: #999;
  font-size: 28rpx;
}
</style> 