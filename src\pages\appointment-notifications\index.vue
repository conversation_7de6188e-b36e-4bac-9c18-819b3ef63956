<template>
  <view class="container">
      <!-- 接收门诊消息提示 -->
      <view class="notification-banner">
        <text class="banner-text">接收门诊消息</text>
        <text class="banner-subtext">查看患者预约和就诊提醒</text>
      </view>
      
      <!-- 消息筛选器 -->
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index" 
          class="tab" 
          :class="{ active: activeTabIndex === index }"
          @tap="switchTab(index)"
        >
          <text class="tab-text">{{ tab.name }}</text>
          <view v-if="tab.unread > 0 && index !== 0" class="tab-badge">{{ tab.unread }}</view>
        </view>
      </view>
      
      <!-- 消息列表 -->
      <view class="notification-list">
        <view v-if="filteredNotifications.length === 0" class="empty-state">
          <text class="empty-icon">📭</text>
          <text class="empty-text">暂无{{ tabs[activeTabIndex].name }}消息</text>
        </view>
        
        <view
          v-for="(notification, index) in filteredNotifications"
          :key="index"
          class="notification-card"
          :class="{ unread: !notification.read && activeTabIndex !== 0 }"
          @tap="viewNotification(notification)"
        >
          <view class="notification-header">
            <view class="patient-info">
              <text class="patient-phone">{{ formatPhone(notification.phone) }}</text>
              <text class="visit-type" :class="notification.visitType">{{ notification.visitType === 'first' ? '初诊' : '复诊' }}</text>
            </view>
            <text class="notification-time">{{ formatTime(notification.time) }}</text>
          </view>
          
          <view class="notification-content">
            <text class="notification-title">{{ notification.title }}</text>
            <text class="notification-desc">{{ notification.description }}</text>
          </view>
          
          <view class="notification-footer">
            <text class="notification-type" :class="notification.type">{{ getTypeText(notification.type) }}</text>
            
            <view class="notification-actions">
              <button 
                v-if="notification.type === 'appointment'" 
                class="action-button confirm"
                @tap.stop="confirmAppointment(notification)"
              >确认</button>
              
              <button 
                v-if="notification.type === 'appointment'" 
                class="action-button reject"
                @tap.stop="rejectAppointment(notification)"
              >拒绝</button>
              
              <button
                v-if="notification.type === 'cancellation' && activeTabIndex !== 0"
                class="action-button"
                :class="{ read: notification.read }"
                @tap.stop="markAsRead(notification)"
              >{{ notification.read ? '已读' : '未读' }}</button>

              <button
                v-if="notification.type === 'reminder' && activeTabIndex !== 0"
                class="action-button"
                :class="{ read: notification.read }"
                @tap.stop="markAsRead(notification)"
              >{{ notification.read ? '已读' : '未读' }}</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 删除已读按钮 - 只在取消通知标签页显示 -->
      <view class="delete-read-container" v-if="activeTabIndex === 1 && hasReadCancellationNotifications">
        <button class="delete-read-button" @tap="deleteReadNotifications">删除已读</button>
      </view>
  </view>
</template>



<script setup>
import { ref, computed, onMounted } from 'vue';

// 消息类型标签
const tabs = ref([
  { name: '全部', unread: 0 },
  { name: '取消通知', unread: 0 },
  { name: '就诊提醒', unread: 0 }
]);

// 当前选中的标签
const activeTabIndex = ref(0);

// 所有通知
const notifications = ref([]);

// 根据标签筛选的通知
const filteredNotifications = computed(() => {
  if (activeTabIndex.value === 0) {
    return notifications.value;
  } else if (activeTabIndex.value === 1) {
    return notifications.value.filter(n => n.type === 'cancellation');
  } else {
    return notifications.value.filter(n => n.type === 'reminder');
  }
});

// 检查是否有已读通知
const hasReadNotifications = computed(() => {
  return notifications.value.some(n => n.read);
});

// 检查是否有已读的取消通知
const hasReadCancellationNotifications = computed(() => {
  return notifications.value.some(n => n.type === 'cancellation' && n.read);
});

// 页面加载时
onMounted(() => {
  // 获取通知数据
  fetchNotifications();
});

// 获取通知数据
const fetchNotifications = () => {
  // 模拟数据，实际应该从API获取
  const mockData = [
    {
      id: '1',
      type: 'cancellation',
      phone: '13812345678',
      visitType: 'first',
      title: '预约取消通知',
      description: '患者13812345678取消了2023年11月15日 09:00-10:00的口腔正畸门诊预约',
      time: new Date(new Date().getTime() - 36 * 60 * 1000), // 36分钟前
      read: false
    },
    {
      id: '2',
      type: 'reminder',
      phone: '13987654321',
      visitType: 'return',
      title: '今日就诊提醒',
      description: '患者13987654321今日14:30有预约，请做好准备',
      time: new Date(new Date().getTime() - 2 * 60 * 60 * 1000), // 2小时前
      read: true
    },
    {
      id: '3',
      type: 'cancellation',
      phone: '13700001111',
      visitType: 'return',
      title: '预约取消通知',
      description: '患者13700001111取消了2023年11月16日 11:00-12:00的口腔种植门诊预约',
      time: new Date(new Date().getTime() - 5 * 60 * 60 * 1000), // 5小时前
      read: false
    },
    {
      id: '4',
      type: 'reminder',
      phone: '13600002222',
      visitType: 'first',
      title: '明日就诊提醒',
      description: '患者13600002222明日10:00有预约，请提前准备病历',
      time: new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000), // 1天前
      read: false
    },
    {
      id: '5',
      type: 'reminder',
      phone: '13511112222',
      visitType: 'first',
      title: '今日就诊提醒',
      description: '患者13511112222今日16:00有预约，请做好准备',
      time: new Date(new Date().getTime() - 30 * 60 * 1000), // 30分钟前
      read: true
    },
    {
      id: '6',
      type: 'cancellation',
      phone: '13422223333',
      visitType: 'return',
      title: '预约取消通知',
      description: '患者13422223333取消了2023年11月17日 15:00-16:00的口腔修复门诊预约',
      time: new Date(new Date().getTime() - 3 * 60 * 60 * 1000), // 3小时前
      read: true
    }
  ];
  
  notifications.value = mockData;
  
  // 更新未读数量
  updateUnreadCount();
};

// 更新未读消息数量
const updateUnreadCount = () => {
  const allUnread = notifications.value.filter(n => !n.read).length;
  const cancellationUnread = notifications.value.filter(n => n.type === 'cancellation' && !n.read).length;
  const reminderUnread = notifications.value.filter(n => n.type === 'reminder' && !n.read).length;
  
  tabs.value[0].unread = allUnread;
  tabs.value[1].unread = cancellationUnread;
  tabs.value[2].unread = reminderUnread;
};

// 切换标签
const switchTab = (index) => {
  activeTabIndex.value = index;
};

// 查看通知详情
const viewNotification = (notification) => {
  // 标记为已读
  if (!notification.read) {
    notification.read = true;
    updateUnreadCount();
  }
  
  // 实际应用中可能会跳转到详情页或显示详情弹窗
  uni.showToast({
    title: '查看详情: ' + notification.title,
    icon: 'none'
  });
};

// 确认预约
const confirmAppointment = (notification) => {
  uni.showModal({
    title: '确认预约',
    content: `确定接受患者${notification.phone}的预约请求吗？`,
    success: (res) => {
      if (res.confirm) {
        // 模拟API请求
        setTimeout(() => {
          // 更新通知状态
          notification.read = true;
          notification.description = notification.description + ' [已确认]';
          updateUnreadCount();
          
          uni.showToast({
            title: '已确认预约',
            icon: 'success'
          });
        }, 500);
      }
    }
  });
};

// 拒绝预约
const rejectAppointment = (notification) => {
  uni.showModal({
    title: '拒绝预约',
    content: `确定拒绝患者${notification.phone}的预约请求吗？`,
    success: (res) => {
      if (res.confirm) {
        // 模拟API请求
        setTimeout(() => {
          // 更新通知状态
          notification.read = true;
          notification.description = notification.description + ' [已拒绝]';
          updateUnreadCount();
          
          uni.showToast({
            title: '已拒绝预约',
            icon: 'success'
          });
        }, 500);
      }
    }
  });
};

// 标记为已读
const markAsRead = (notification) => {
  // 如果已经是已读状态，则不执行任何操作
  if (notification.read) {
    return;
  }

  notification.read = true;
  updateUnreadCount();

  uni.showToast({
    title: '已标记为已读',
    icon: 'success'
  });
};

// 删除已读通知 - 只删除取消通知类型的已读消息
const deleteReadNotifications = () => {
  const readCancellationCount = notifications.value.filter(n => n.type === 'cancellation' && n.read).length;

  uni.showModal({
    title: '删除已读取消通知',
    content: `确定要删除${readCancellationCount}条已读取消通知吗？`,
    success: (res) => {
      if (res.confirm) {
        // 只过滤掉已读的取消通知
        notifications.value = notifications.value.filter(n => !(n.type === 'cancellation' && n.read));
        updateUnreadCount();

        uni.showToast({
          title: '已删除已读取消通知',
          icon: 'success'
        });
      }
    }
  });
};

// 格式化时间显示
const formatTime = (time) => {
  const now = new Date();
  const diff = now - time;
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return Math.floor(diff / (60 * 1000)) + '分钟前';
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
  }
  
  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
  }
  
  // 大于30天，显示具体日期
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 获取通知类型文本
const getTypeText = (type) => {
  switch (type) {
    case 'cancellation':
      return '取消通知';
    case 'reminder':
      return '就诊提醒';
    default:
      return '通知';
  }
};

// 格式化手机号显示
const formatPhone = (phone) => {
  return phone;
};


</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 30rpx;
}

.notification-banner {
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  color: #fff;
}

.banner-text {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.banner-subtext {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 筛选标签样式 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: 25rpx 0;
  text-align: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tab.active {
  background-color: #8e99f3;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
}

.tab.active .tab-text {
  color: #fff;
  font-weight: bold;
}

.tab-badge {
  position: absolute;
  top: 10rpx;
  right: 20%;
  background-color: #ff6b6b;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
}

/* 通知列表样式 */
.notification-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

.notification-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.notification-card.unread {
  border-left: 8rpx solid #ff6b6b;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.patient-info {
  display: flex;
  flex-direction: column;
}

.patient-phone {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.visit-type {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: #fff;
  font-weight: bold;
}

.visit-type.first {
  background-color: #4CAF50;
}

.visit-type.return {
  background-color: #2196F3;
}

.notification-time {
  font-size: 24rpx;
  color: #999;
}

.notification-content {
  margin-bottom: 20rpx;
}

.notification-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.notification-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.notification-type {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  color: #666;
}

.notification-type.cancellation {
  background-color: rgba(142, 153, 243, 0.2);
  color: #8e99f3;
}

.notification-type.reminder {
  background-color: rgba(255, 206, 84, 0.2);
  color: #ffce54;
}

.notification-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  font-size: 24rpx;
  padding: 6rpx 30rpx;
  border-radius: 30rpx;
  background-color: #8e99f3;
  color: #fff;
  border: none;
  line-height: 1.8;
}

.action-button.read {
  background-color: #ccc;
  color: #666;
}

.action-button.confirm {
  background-color: #8e99f3;
}

.action-button.reject {
  background-color: #ff6b6b;
}

/* 删除已读按钮样式 */
.delete-read-container {
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: center;
}

.delete-read-button {
  background-color: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.delete-read-button:active {
  background-color: #ff5252;
  transform: scale(0.98);
}
</style>