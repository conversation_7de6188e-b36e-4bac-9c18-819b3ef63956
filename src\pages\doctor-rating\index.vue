<template>
  <view class="container">
    <view class="header">
      <text class="title">医生评分</text>
      <text class="subtitle">请为您的就诊医生提供评价和反馈</text>
    </view>
    
    <view class="content">
      <!-- 医生列表 -->
      <view class="doctor-list">
        <view v-for="(doctor, index) in doctorList" :key="index" class="doctor-card" @tap="selectDoctor(doctor)">
          <view class="doctor-header">
            <view class="doctor-info">
              <view class="doctor-avatar">
                <text class="avatar-text">{{ doctor.name.substring(0, 1) }}</text>
              </view>
              <view class="doctor-details">
                <text class="doctor-name">{{ doctor.name }}</text>
                <text class="doctor-specialty">{{ doctor.specialty }}</text>
                <text class="doctor-hospital">{{ doctor.hospital }}</text>
              </view>
            </view>
            <view class="appointment-type-badge" :class="doctor.appointmentType === '初诊' ? 'first-visit' : 'follow-up'">
              <text class="badge-text">{{ doctor.appointmentType }}</text>
            </view>
          </view>

          <view class="visit-info">
            <view class="visit-time">
              <view class="time-icon">🕒</view>
              <view class="time-details">
                <text class="visit-date">{{ formatVisitDate(doctor.visitDate) }}</text>
                <text class="visit-time-text">{{ doctor.visitTime }}</text>
              </view>
            </view>
          </view>

          <view class="doctor-rating-preview">
            <view class="stars">
              <text v-for="i in 5" :key="i" class="star" :class="{ 'filled': i <= doctor.rating }">★</text>
            </view>
            <text class="rating-text">{{ doctor.rating || '未评分' }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 评分弹窗 -->
    <view class="rating-modal" v-if="showRatingModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">评价医生</text>
          <text class="close-button" @tap="closeRatingModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="selected-doctor">
            <text class="selected-doctor-name">{{ selectedDoctor.name }}</text>
            <text class="selected-doctor-specialty">{{ selectedDoctor.specialty }}</text>
          </view>
          
          <view class="rating-section">
            <text class="rating-label">服务态度</text>
            <view class="stars-selection">
              <text 
                v-for="i in 5" 
                :key="i" 
                class="star-selectable" 
                :class="{ 'filled': i <= serviceRating }"
                @tap="setServiceRating(i)"
              >★</text>
            </view>
          </view>
          
          <view class="rating-section">
            <text class="rating-label">专业水平</text>
            <view class="stars-selection">
              <text 
                v-for="i in 5" 
                :key="i" 
                class="star-selectable" 
                :class="{ 'filled': i <= professionalRating }"
                @tap="setProfessionalRating(i)"
              >★</text>
            </view>
          </view>
          
          <view class="rating-section">
            <text class="rating-label">诊疗效果</text>
            <view class="stars-selection">
              <text 
                v-for="i in 5" 
                :key="i" 
                class="star-selectable" 
                :class="{ 'filled': i <= effectRating }"
                @tap="setEffectRating(i)"
              >★</text>
            </view>
          </view>
          
          <view class="comment-section">
            <text class="comment-label">评价内容</text>
            <textarea class="comment-input" v-model="commentText" placeholder="请输入您的评价内容（选填）" />
          </view>
          
          <button class="submit-button" @tap="submitRating">提交评价</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 医生列表数据
const doctorList = ref([
  {
    id: '1',
    name: '张医生',
    specialty: '口腔正畸',
    hospital: '阳光口腔诊所 - 海淀区',
    rating: 4.5,
    visitDate: '2025-01-22', // 昨天
    visitTime: '14:00-14:30',
    appointmentType: '复诊'
  },
  {
    id: '2',
    name: '李医生',
    specialty: '牙体牙髓病',
    hospital: '微笑口腔 - 朝阳区',
    rating: 4.8,
    visitDate: '2025-01-20', // 3天前
    visitTime: '09:00-09:30',
    appointmentType: '初诊'
  },
  {
    id: '3',
    name: '王医生',
    specialty: '口腔修复',
    hospital: '健康口腔 - 西城区',
    rating: 0,
    visitDate: '2025-01-17', // 6天前
    visitTime: '16:00-16:30',
    appointmentType: '复诊'
  },
  {
    id: '4',
    name: '刘医生',
    specialty: '口腔种植',
    hospital: '爱牙口腔 - 东城区',
    rating: 4.2,
    visitDate: '2025-01-10', // 超过一周，显示具体日期
    visitTime: '10:30-11:00',
    appointmentType: '初诊'
  },
  {
    id: '5',
    name: '陈医生',
    specialty: '牙周病科',
    hospital: '康美口腔 - 朝阳区',
    rating: 4.6,
    visitDate: '2024-12-15', // 超过一周，显示具体日期
    visitTime: '15:30-16:00',
    appointmentType: '复诊'
  },
  {
    id: '6',
    name: '黄医生',
    specialty: '儿童口腔',
    hospital: '童心口腔 - 西城区',
    rating: 4.3,
    visitDate: '2024-11-28', // 超过一周，显示具体日期
    visitTime: '08:30-09:00',
    appointmentType: '初诊'
  }
]);

// 评分相关状态
const showRatingModal = ref(false);
const selectedDoctor = ref({});
const serviceRating = ref(0);
const professionalRating = ref(0);
const effectRating = ref(0);
const commentText = ref('');

// 选择医生进行评分
const selectDoctor = (doctor) => {
  selectedDoctor.value = doctor;
  serviceRating.value = 0;
  professionalRating.value = 0;
  effectRating.value = 0;
  commentText.value = '';
  showRatingModal.value = true;
};

// 关闭评分弹窗
const closeRatingModal = () => {
  showRatingModal.value = false;
};

// 设置服务态度评分
const setServiceRating = (rating) => {
  serviceRating.value = rating;
};

// 设置专业水平评分
const setProfessionalRating = (rating) => {
  professionalRating.value = rating;
};

// 设置诊疗效果评分
const setEffectRating = (rating) => {
  effectRating.value = rating;
};

// 计算总评分
const totalRating = computed(() => {
  if (serviceRating.value === 0 || professionalRating.value === 0 || effectRating.value === 0) {
    return 0;
  }
  return ((serviceRating.value + professionalRating.value + effectRating.value) / 3).toFixed(1);
});

// 格式化就诊日期
const formatVisitDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  // 一周内显示相对时间
  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    // 超过一周显示具体日期 YYYY.M.D 格式
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // getMonth() 返回 0-11
    const day = date.getDate();
    return `${year}.${month}.${day}`;
  }
};

// 提交评分
const submitRating = () => {
  if (totalRating.value === 0) {
    uni.showToast({
      title: '请完成所有评分项',
      icon: 'none'
    });
    return;
  }
  
  // 更新医生评分
  const index = doctorList.value.findIndex(d => d.id === selectedDoctor.value.id);
  if (index !== -1) {
    doctorList.value[index].rating = parseFloat(totalRating.value);
    
    // 这里应该有一个API请求来保存评分到服务器
    // 模拟保存成功
    uni.showToast({
      title: '评分提交成功',
      icon: 'success'
    });
    
    closeRatingModal();
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 40rpx 30rpx;
  color: #fff;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.doctor-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.doctor-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    border-color: #8e99f3;
    box-shadow: 0 6rpx 20rpx rgba(142, 153, 243, 0.2);
  }
}

.doctor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.appointment-type-badge {
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  font-size: 22rpx;

  &.first-visit {
    background-color: #e8f5e8;
    color: #4cd964;
    border: 1rpx solid #4cd964;
  }

  &.follow-up {
    background-color: #fff3e0;
    color: #ff9500;
    border: 1rpx solid #ff9500;
  }
}

.badge-text {
  font-weight: 500;
}

.doctor-avatar {
  width: 100rpx;
  height: 100rpx;
  background-color: #8e99f3;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-text {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.doctor-details {
  flex: 1;
}

.doctor-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.doctor-specialty {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
  display: block;
}

.doctor-hospital {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.visit-info {
  background-color: #f8f9ff;
  border-radius: 15rpx;
  padding: 20rpx;
  margin: 10rpx 0;
  border: 1rpx solid #e6f3ff;
}

.visit-time {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.time-icon {
  font-size: 28rpx;
  color: #8e99f3;
}

.time-details {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.visit-date {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.visit-time-text {
  font-size: 24rpx;
  color: #666;
}

.doctor-rating-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stars {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 28rpx;
  color: #ddd;

  &.filled {
    color: #ffd700;
  }
}

.rating-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  background-color: #f0f8ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e6f3ff;
}

/* 评分弹窗样式 */
.rating-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  background-color: #8e99f3;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.close-button {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.modal-body {
  padding: 30rpx;
}

.selected-doctor {
  margin-bottom: 30rpx;
  text-align: center;
}

.selected-doctor-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
}

.selected-doctor-specialty {
  font-size: 28rpx;
  color: #666;
}

.rating-section {
  margin-bottom: 30rpx;
}

.rating-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.stars-selection {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.star-selectable {
  font-size: 50rpx;
  color: #ddd;
  cursor: pointer;
  
  &.filled {
    color: #ffce54;
  }
}

.comment-section {
  margin-bottom: 30rpx;
}

.comment-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.comment-input {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.submit-button {
  background-color: #8e99f3;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 10rpx;
  text-align: center;
  border: none;
}
</style> 