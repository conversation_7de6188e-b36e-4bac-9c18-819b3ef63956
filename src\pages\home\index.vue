<template>
  <view class="container">
    <!-- 简化的地址输入框 -->
    <view class="address-section">
      <view class="section-title">选择您所在的区域</view>
      <view class="search-box">
        <input
          type="text"
          class="search-input"
          placeholder="请输入您所在的城市/区县"
          v-model="address"
          @input="onAddressInput"
          @confirm="searchLocation"
        />
        <text class="search-icon">🔍</text>

        <!-- 搜索结果下拉框 -->
        <view class="search-dropdown" v-if="showSearchResults && filteredAreas.length > 0">
          <view
            class="search-result-item"
            v-for="(area, index) in filteredAreas"
            :key="index"
            @tap="selectArea(area)"
          >
            {{ area }}
          </view>
        </view>
      </view>

      <!-- 历史搜索记录 -->
      <view class="history-section" v-if="searchHistory.length > 0 && !selectedAreaInfo">
        <view class="history-header">
          <text class="history-title">历史搜索</text>
          <text class="clear-history" @tap="clearHistory">清空</text>
        </view>
        <view class="quick-area-select">
          <view
            class="area-tag"
            v-for="(area, index) in searchHistory"
            :key="index"
            @tap="selectArea(area)"
          >
            {{ area }}
          </view>
        </view>
      </view>
    </view>

    <!-- 口腔健康状况卡片 -->
    <view class="dental-health-section" v-if="latestDentalRecord">
      <dental-health-card :record="latestDentalRecord" />
    </view>

    <!-- 专科推荐分析 -->
    <view class="specialty-recommendation-section" v-if="latestDentalRecord && recommendedSpecialties.length > 0">
      <view class="specialty-header">
        <text class="specialty-title">专科推荐分析</text>
        <text class="specialty-subtitle">根据您的口腔健康状况智能分析</text>
      </view>

      <view class="analysis-content">
        <view class="analysis-summary">
          <text class="analysis-text">{{ getHealthAnalysis() }}</text>
        </view>

        <view class="specialty-list">
          <view
            class="specialty-card"
            v-for="(specialty, index) in recommendedSpecialties"
            :key="index"
          >
            <view class="specialty-icon">
              <text class="icon-text">{{ specialty.icon }}</text>
            </view>
            <view class="specialty-info">
              <text class="specialty-name">{{ specialty.name }}</text>
              <text class="specialty-reason">{{ specialty.reason }}</text>
              <text class="specialty-priority">推荐指数: {{ specialty.priority }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 智能推荐提示 -->
    <view class="smart-recommendation-tip" v-if="latestDentalRecord && !selectedAreaInfo">
      <text class="tip-content">确认您的信息，为您智能推荐医生</text>
    </view>

    <!-- 推荐医生列表 -->
    <view class="recommended-doctors-section" v-if="selectedAreaInfo && recommendedDoctors.length > 0">
      <view class="doctors-header">
        <text class="doctors-title">{{ selectedAreaInfo }}推荐医生</text>
        <text class="doctors-subtitle">根据您的位置为您智能推荐</text>
      </view>

      <scroll-view class="doctors-list" scroll-y>
        <view
          class="doctor-card"
          v-for="(doctor, index) in recommendedDoctors"
          :key="index"
          @tap="selectDoctor(doctor)"
        >
          <view class="doctor-avatar">
            <image class="avatar-image" :src="doctor.avatar" mode="aspectFill"></image>
          </view>
          <view class="doctor-info">
            <view class="doctor-name">{{ doctor.name }}</view>
            <view class="doctor-specialty">{{ doctor.specialty }}</view>
            <view class="doctor-hospital">{{ doctor.hospital }}</view>
            <view class="doctor-rating">
              <text class="rating-score">{{ doctor.rating }}</text>
              <text class="rating-text">分</text>
              <text class="rating-count">({{ doctor.ratingCount }}人评价)</text>
            </view>
          </view>
          <view class="doctor-distance">
            <text class="distance-text">{{ doctor.distance }}km</text>
          </view>
        </view>
      </scroll-view>

      <view class="view-more-section">
        <button class="view-more-btn" @tap="viewAllDoctors">查看更多医生</button>
      </view>

      <!-- 不满意反馈按钮 -->
      <view class="feedback-section">
        <button class="feedback-btn" @tap="showFeedbackDialog">如不满意，说说自己的情况</button>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import DentalHealthCard from '@/components/DentalHealthCard.vue';
import dentalMock from '@/mock/dental';

// 地址相关数据
const address = ref('');
const showSearchResults = ref(false);
const searchHistory = ref([]);
const selectedAreaInfo = ref(''); // 选中的区域信息

// 口腔健康记录
const latestDentalRecord = ref(null);

// 推荐医生数据
const recommendedDoctors = ref([]);

// 专科推荐数据
const recommendedSpecialties = ref([]);

// 所有可选区域 - 格式为 { city: '城市名', area: '区县名', fullName: '城市名+区县名' }
const allAreas = [
  // 北京市
  { city: '北京市', area: '东城区', fullName: '北京市东城区' },
  { city: '北京市', area: '西城区', fullName: '北京市西城区' },
  { city: '北京市', area: '朝阳区', fullName: '北京市朝阳区' },
  { city: '北京市', area: '丰台区', fullName: '北京市丰台区' },
  { city: '北京市', area: '石景山区', fullName: '北京市石景山区' },
  { city: '北京市', area: '海淀区', fullName: '北京市海淀区' },
  { city: '北京市', area: '门头沟区', fullName: '北京市门头沟区' },
  { city: '北京市', area: '房山区', fullName: '北京市房山区' },
  { city: '北京市', area: '通州区', fullName: '北京市通州区' },
  { city: '北京市', area: '顺义区', fullName: '北京市顺义区' },
  { city: '北京市', area: '昌平区', fullName: '北京市昌平区' },
  { city: '北京市', area: '大兴区', fullName: '北京市大兴区' },
  { city: '北京市', area: '怀柔区', fullName: '北京市怀柔区' },
  { city: '北京市', area: '平谷区', fullName: '北京市平谷区' },
  { city: '北京市', area: '密云区', fullName: '北京市密云区' },
  { city: '北京市', area: '延庆区', fullName: '北京市延庆区' },
  
  // 福建省 - 福州市
  { city: '福州市', area: '鼓楼区', fullName: '福州市鼓楼区' },
  { city: '福州市', area: '台江区', fullName: '福州市台江区' },
  { city: '福州市', area: '仓山区', fullName: '福州市仓山区' },
  { city: '福州市', area: '马尾区', fullName: '福州市马尾区' },
  { city: '福州市', area: '晋安区', fullName: '福州市晋安区' },
  { city: '福州市', area: '长乐区', fullName: '福州市长乐区' },
  { city: '福州市', area: '福清市', fullName: '福州市福清市' },
  { city: '福州市', area: '闽侯县', fullName: '福州市闽侯县' },
  { city: '福州市', area: '连江县', fullName: '福州市连江县' },
  { city: '福州市', area: '罗源县', fullName: '福州市罗源县' },
  { city: '福州市', area: '闽清县', fullName: '福州市闽清县' },
  { city: '福州市', area: '永泰县', fullName: '福州市永泰县' },
  { city: '福州市', area: '平潭县', fullName: '福州市平潭县' },
  
  // 福建省 - 厦门市
  { city: '厦门市', area: '思明区', fullName: '厦门市思明区' },
  { city: '厦门市', area: '海沧区', fullName: '厦门市海沧区' },
  { city: '厦门市', area: '湖里区', fullName: '厦门市湖里区' },
  { city: '厦门市', area: '集美区', fullName: '厦门市集美区' },
  { city: '厦门市', area: '同安区', fullName: '厦门市同安区' },
  { city: '厦门市', area: '翔安区', fullName: '厦门市翔安区' },
  
  // 福建省 - 泉州市
  { city: '泉州市', area: '鲤城区', fullName: '泉州市鲤城区' },
  { city: '泉州市', area: '丰泽区', fullName: '泉州市丰泽区' },
  { city: '泉州市', area: '洛江区', fullName: '泉州市洛江区' },
  { city: '泉州市', area: '泉港区', fullName: '泉州市泉港区' },
  { city: '泉州市', area: '石狮市', fullName: '泉州市石狮市' },
  { city: '泉州市', area: '晋江市', fullName: '泉州市晋江市' },
  { city: '泉州市', area: '南安市', fullName: '泉州市南安市' },
  { city: '泉州市', area: '惠安县', fullName: '泉州市惠安县' },
  { city: '泉州市', area: '安溪县', fullName: '泉州市安溪县' },
  { city: '泉州市', area: '永春县', fullName: '泉州市永春县' },
  { city: '泉州市', area: '德化县', fullName: '泉州市德化县' },
  { city: '泉州市', area: '金门县', fullName: '泉州市金门县' },
  
  // 福建省 - 漳州市
  { city: '漳州市', area: '芗城区', fullName: '漳州市芗城区' },
  { city: '漳州市', area: '龙文区', fullName: '漳州市龙文区' },
  { city: '漳州市', area: '龙海区', fullName: '漳州市龙海区' },
  { city: '漳州市', area: '漳浦县', fullName: '漳州市漳浦县' },
  { city: '漳州市', area: '云霄县', fullName: '漳州市云霄县' },
  { city: '漳州市', area: '诏安县', fullName: '漳州市诏安县' },
  { city: '漳州市', area: '长泰县', fullName: '漳州市长泰县' },
  { city: '漳州市', area: '东山县', fullName: '漳州市东山县' },
  { city: '漳州市', area: '南靖县', fullName: '漳州市南靖县' },
  { city: '漳州市', area: '平和县', fullName: '漳州市平和县' },
  { city: '漳州市', area: '华安县', fullName: '漳州市华安县' },
  
  // 福建省 - 莆田市
  { city: '莆田市', area: '城厢区', fullName: '莆田市城厢区' },
  { city: '莆田市', area: '涵江区', fullName: '莆田市涵江区' },
  { city: '莆田市', area: '荔城区', fullName: '莆田市荔城区' },
  { city: '莆田市', area: '秀屿区', fullName: '莆田市秀屿区' },
  { city: '莆田市', area: '仙游县', fullName: '莆田市仙游县' },
  
  // 福建省 - 三明市
  { city: '三明市', area: '梅列区', fullName: '三明市梅列区' },
  { city: '三明市', area: '三元区', fullName: '三明市三元区' },
  { city: '三明市', area: '永安市', fullName: '三明市永安市' },
  { city: '三明市', area: '明溪县', fullName: '三明市明溪县' },
  { city: '三明市', area: '清流县', fullName: '三明市清流县' },
  { city: '三明市', area: '宁化县', fullName: '三明市宁化县' },
  { city: '三明市', area: '大田县', fullName: '三明市大田县' },
  { city: '三明市', area: '尤溪县', fullName: '三明市尤溪县' },
  { city: '三明市', area: '沙县', fullName: '三明市沙县' },
  { city: '三明市', area: '将乐县', fullName: '三明市将乐县' },
  { city: '三明市', area: '泰宁县', fullName: '三明市泰宁县' },
  { city: '三明市', area: '建宁县', fullName: '三明市建宁县' },
  
  // 福建省 - 南平市
  { city: '南平市', area: '延平区', fullName: '南平市延平区' },
  { city: '南平市', area: '建阳区', fullName: '南平市建阳区' },
  { city: '南平市', area: '邵武市', fullName: '南平市邵武市' },
  { city: '南平市', area: '武夷山市', fullName: '南平市武夷山市' },
  { city: '南平市', area: '建瓯市', fullName: '南平市建瓯市' },
  { city: '南平市', area: '顺昌县', fullName: '南平市顺昌县' },
  { city: '南平市', area: '浦城县', fullName: '南平市浦城县' },
  { city: '南平市', area: '光泽县', fullName: '南平市光泽县' },
  { city: '南平市', area: '松溪县', fullName: '南平市松溪县' },
  { city: '南平市', area: '政和县', fullName: '南平市政和县' },
  
  // 福建省 - 龙岩市
  { city: '龙岩市', area: '新罗区', fullName: '龙岩市新罗区' },
  { city: '龙岩市', area: '永定区', fullName: '龙岩市永定区' },
  { city: '龙岩市', area: '漳平市', fullName: '龙岩市漳平市' },
  { city: '龙岩市', area: '长汀县', fullName: '龙岩市长汀县' },
  { city: '龙岩市', area: '上杭县', fullName: '龙岩市上杭县' },
  { city: '龙岩市', area: '武平县', fullName: '龙岩市武平县' },
  { city: '龙岩市', area: '连城县', fullName: '龙岩市连城县' },
  
  // 福建省 - 宁德市
  { city: '宁德市', area: '蕉城区', fullName: '宁德市蕉城区' },
  { city: '宁德市', area: '福安市', fullName: '宁德市福安市' },
  { city: '宁德市', area: '福鼎市', fullName: '宁德市福鼎市' },
  { city: '宁德市', area: '霞浦县', fullName: '宁德市霞浦县' },
  { city: '宁德市', area: '古田县', fullName: '宁德市古田县' },
  { city: '宁德市', area: '屏南县', fullName: '宁德市屏南县' },
  { city: '宁德市', area: '寿宁县', fullName: '宁德市寿宁县' },
  { city: '宁德市', area: '周宁县', fullName: '宁德市周宁县' },
  { city: '宁德市', area: '柘荣县', fullName: '宁德市柘荣县' }
];

// 模拟医生数据 - 按区域分组
const doctorsByArea = {
  '漳州市芗城区': [
    {
      id: 'zz001',
      name: '陈志华医生',
      specialty: '口腔正畸',
      hospital: '漳州市第一医院口腔科',
      avatar: '/static/dental/sample1.png',
      rating: 4.9,
      ratingCount: 156,
      distance: 1.2,
      experience: '15年',
      introduction: '专注口腔正畸治疗，擅长各类牙齿矫正'
    },
    {
      id: 'zz002',
      name: '林美玲医生',
      specialty: '牙周病科',
      hospital: '漳州口腔医院',
      avatar: '/static/dental/toothbrush.png',
      rating: 4.8,
      ratingCount: 203,
      distance: 0.8,
      experience: '12年',
      introduction: '牙周病治疗专家，擅长牙龈炎、牙周炎治疗'
    },
    {
      id: 'zz003',
      name: '黄建国医生',
      specialty: '口腔外科',
      hospital: '漳州市中医院口腔科',
      avatar: '/static/dental/dental_kit.png',
      rating: 4.7,
      ratingCount: 89,
      distance: 2.1,
      experience: '18年',
      introduction: '口腔外科手术专家，擅长智齿拔除、种植牙'
    }
  ],
  '漳州市龙文区': [
    {
      id: 'zz004',
      name: '王丽华医生',
      specialty: '儿童口腔',
      hospital: '龙文区人民医院',
      avatar: '/static/dental/kids_toothpaste.png',
      rating: 4.9,
      ratingCount: 134,
      distance: 1.5,
      experience: '10年',
      introduction: '儿童口腔专家，温和耐心，深受小朋友喜爱'
    },
    {
      id: 'zz005',
      name: '张明医生',
      specialty: '口腔修复',
      hospital: '龙文口腔诊所',
      avatar: '/static/dental/toothpaste.png',
      rating: 4.6,
      ratingCount: 78,
      distance: 2.3,
      experience: '14年',
      introduction: '口腔修复专家，擅长烤瓷牙、全瓷牙修复'
    }
  ],
  '北京市海淀区': [
    {
      id: 'bj001',
      name: '李教授',
      specialty: '口腔正畸',
      hospital: '北京大学口腔医院',
      avatar: '/static/dental/diandongyashua.png',
      rating: 4.9,
      ratingCount: 312,
      distance: 0.5,
      experience: '20年',
      introduction: '知名口腔正畸专家，北大口腔医院主任医师'
    },
    {
      id: 'bj002',
      name: '刘医生',
      specialty: '种植牙科',
      hospital: '海淀区口腔医院',
      avatar: '/static/dental/mouthwash.png',
      rating: 4.8,
      ratingCount: 245,
      distance: 1.8,
      experience: '16年',
      introduction: '种植牙专家，成功案例超过3000例'
    }
  ],
  '福州市鼓楼区': [
    {
      id: 'fz001',
      name: '郑医生',
      specialty: '口腔内科',
      hospital: '福州市第一医院',
      avatar: '/static/dental/floss.png',
      rating: 4.7,
      ratingCount: 167,
      distance: 1.1,
      experience: '13年',
      introduction: '口腔内科专家，擅长根管治疗、牙体修复'
    }
  ]
};

// 根据输入过滤区域
const filteredAreas = computed(() => {
  if (!address.value) return [];
  
  const keyword = address.value.toLowerCase();
  
  return allAreas.filter(item => {
    return item.fullName.toLowerCase().includes(keyword) || 
           item.city.toLowerCase().includes(keyword) || 
           item.area.toLowerCase().includes(keyword);
  }).map(item => item.fullName).slice(0, 8); // 最多显示8个结果
});

// 监听地址输入
const onAddressInput = () => {
  showSearchResults.value = true;
};

// 选择区域
const selectArea = (area) => {
  address.value = area;
  showSearchResults.value = false;
  selectedAreaInfo.value = area;

  // 保存选择的地址到本地存储
  uni.setStorageSync('userAddress', area);
  console.log('保存用户选择的地址:', area);

  // 添加到历史记录
  addToHistory(area);

  // 根据选择的区域推荐医生
  loadRecommendedDoctors(area);
};

// 添加到历史记录
const addToHistory = (area) => {
  // 如果已经存在，先移除
  const index = searchHistory.value.indexOf(area);
  if (index !== -1) {
    searchHistory.value.splice(index, 1);
  }
  
  // 添加到历史记录的开头
  searchHistory.value.unshift(area);
  
  // 最多保留5条历史记录
  if (searchHistory.value.length > 5) {
    searchHistory.value = searchHistory.value.slice(0, 5);
  }
  
  // 保存到本地存储
  saveSearchHistory();
};

// 保存搜索历史到本地存储
const saveSearchHistory = () => {
  try {
    uni.setStorageSync('areaSearchHistory', JSON.stringify(searchHistory.value));
  } catch (e) {
    console.error('保存搜索历史失败', e);
  }
};

// 从本地存储加载搜索历史
const loadSearchHistory = () => {
  try {
    const history = uni.getStorageSync('areaSearchHistory');
    if (history) {
      searchHistory.value = JSON.parse(history);
    }
  } catch (e) {
    console.error('加载搜索历史失败', e);
  }
};

// 清空历史记录
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = [];
        saveSearchHistory();
      }
    }
  });
};

// 搜索位置
const searchLocation = () => {
  if (!address.value) {
    uni.showToast({
      title: '请输入区域',
      icon: 'none'
    });
    return;
  }

  // 保存选择的地址到本地存储
  uni.setStorageSync('userAddress', address.value);
  console.log('搜索保存用户选择的地址:', address.value);

  uni.showToast({
    title: '区域已选择',
    icon: 'success'
  });
  showSearchResults.value = false;

  // 添加到历史记录
  addToHistory(address.value);
};

// 加载推荐医生 - 结合专科推荐
const loadRecommendedDoctors = (area) => {
  // 根据区域获取医生数据
  const doctors = doctorsByArea[area] || [];

  if (doctors.length > 0) {
    let filteredDoctors = [...doctors];

    // 如果有专科推荐，优先推荐对应专科的医生
    if (recommendedSpecialties.value.length > 0) {
      const recommendedSpecialtyNames = recommendedSpecialties.value.map(s => s.name);

      // 按专科匹配度排序
      filteredDoctors = filteredDoctors.sort((a, b) => {
        const aMatch = recommendedSpecialtyNames.some(specialty =>
          a.specialty.includes(specialty.replace('科', '')) ||
          specialty.includes(a.specialty.replace('科', ''))
        );
        const bMatch = recommendedSpecialtyNames.some(specialty =>
          b.specialty.includes(specialty.replace('科', '')) ||
          specialty.includes(b.specialty.replace('科', ''))
        );

        if (aMatch && !bMatch) return -1;
        if (!aMatch && bMatch) return 1;
        return b.rating - a.rating; // 如果专科匹配度相同，按评分排序
      });
    }

    // 显示前3个医生作为推荐
    recommendedDoctors.value = filteredDoctors.slice(0, 3);

    const specialtyText = recommendedSpecialties.value.length > 0
      ? `根据专科推荐为您优选`
      : `为您推荐`;

    uni.showToast({
      title: `${specialtyText}${filteredDoctors.length}位医生`,
      icon: 'success',
      duration: 1500
    });
  } else {
    // 如果该区域没有医生数据，显示默认推荐
    const defaultSpecialty = recommendedSpecialties.value.length > 0
      ? recommendedSpecialties.value[0].name
      : '口腔综合科';

    recommendedDoctors.value = [
      {
        id: 'default001',
        name: '张医生',
        specialty: defaultSpecialty,
        hospital: `${area}人民医院`,
        avatar: '/static/dental/sample1.png',
        rating: 4.5,
        ratingCount: 50,
        distance: 2.0,
        experience: '8年',
        introduction: '口腔综合治疗专家'
      }
    ];

    uni.showToast({
      title: '为您推荐附近医生',
      icon: 'success',
      duration: 1500
    });
  }
};

// 选择医生
const selectDoctor = (doctor) => {
  uni.showToast({
    title: `已选择${doctor.name}`,
    icon: 'success'
  });

  // 保存选择的医生信息和地址信息
  uni.setStorageSync('selectedDoctor', doctor);
  uni.setStorageSync('userAddress', address.value);

  // 跳转到预约时间页面
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/appointment-detail/index'
    });
  }, 1500);
};

// 查看更多医生
const viewAllDoctors = () => {
  // 保存地址信息
  uni.setStorageSync('userAddress', address.value);

  // 跳转到医生选择页面查看所有医生
  uni.navigateTo({
    url: '/pages/doctor-selection/index'
  });
};

// 跳转到情况描述页面
const showFeedbackDialog = () => {
  // 确保保存当前选择的地址
  if (address.value) {
    uni.setStorageSync('userAddress', address.value);
  }

  uni.navigateTo({
    url: '/pages/situation-description/index'
  });
};

// 专科推荐分析函数
const analyzeSpecialtyRecommendation = (record) => {
  if (!record) return [];

  const recommendations = [];
  const healthScore = record.health_score || 0;
  const plaqueRatio = parseFloat((record.plaque_ratio || '0%').replace('%', ''));
  const cariesRatio = parseFloat((record.caries_ratio || '0%').replace('%', ''));
  const cavityRatio = parseFloat((record.cavity_ratio || '0%').replace('%', ''));
  const cleanlinessScore = record.cleanliness_score || 0;

  // 牙周病科推荐逻辑
  if (plaqueRatio > 10 || cleanlinessScore < 70) {
    recommendations.push({
      name: '牙周病科',
      icon: '🦷',
      reason: `牙菌斑比例${plaqueRatio.toFixed(1)}%，清洁度评分${cleanlinessScore}分，建议进行牙周检查`,
      priority: '⭐⭐⭐⭐⭐',
      weight: 5
    });
  }

  // 口腔正畸科推荐逻辑
  if (healthScore < 70) {
    recommendations.push({
      name: '口腔正畸',
      icon: '🔧',
      reason: `健康评分${healthScore}分偏低，可能存在牙齿排列问题，建议正畸检查`,
      priority: '⭐⭐⭐⭐',
      weight: 4
    });
  }

  // 口腔内科推荐逻辑
  if (cariesRatio > 5 || cavityRatio > 3) {
    recommendations.push({
      name: '口腔内科',
      icon: '🩺',
      reason: `龋齿比例${cariesRatio.toFixed(1)}%，牙洞比例${cavityRatio.toFixed(1)}%，建议进行补牙治疗`,
      priority: '⭐⭐⭐⭐⭐',
      weight: 5
    });
  }

  // 口腔外科推荐逻辑
  if (cavityRatio > 5) {
    recommendations.push({
      name: '口腔外科',
      icon: '⚕️',
      reason: `牙洞比例${cavityRatio.toFixed(1)}%较高，可能需要拔牙或手术治疗`,
      priority: '⭐⭐⭐⭐',
      weight: 4
    });
  }

  // 按权重排序，取前2个
  return recommendations
    .sort((a, b) => b.weight - a.weight)
    .slice(0, 2);
};

// 获取健康分析文本
const getHealthAnalysis = () => {
  if (!latestDentalRecord.value) return '';

  const record = latestDentalRecord.value;
  const healthScore = record.health_score || 0;
  const plaqueRatio = parseFloat((record.plaque_ratio || '0%').replace('%', ''));
  const cariesRatio = parseFloat((record.caries_ratio || '0%').replace('%', ''));

  let analysis = '根据您的口腔健康数据分析：';

  if (healthScore < 70) {
    analysis += `健康评分${healthScore}分偏低，`;
  }
  if (plaqueRatio > 10) {
    analysis += `牙菌斑比例${plaqueRatio.toFixed(1)}%偏高，`;
  }
  if (cariesRatio > 5) {
    analysis += `龋齿比例${cariesRatio.toFixed(1)}%需要关注，`;
  }

  analysis += '建议优先就诊以下专科：';
  return analysis;
};

// 获取最新的口腔健康记录
const fetchLatestDentalRecord = () => {
  try {
    // 暂时只使用模拟数据，不进行API请求
    console.log('使用模拟数据');
    const res = dentalMock.getLatestDentalRecord();
    if (res.success) {
      latestDentalRecord.value = res.record;
      // 分析专科推荐
      recommendedSpecialties.value = analyzeSpecialtyRecommendation(res.record);
    }
    
    /* 暂时注释掉API请求部分
    // 显示加载中
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 使用实际API接口
    dentalApi.getLatestDentalRecord().then(res => {
      // 隐藏加载中
      uni.hideLoading();
      
      if (res.success && res.record) {
        latestDentalRecord.value = res.record;
      } else {
        console.error('获取口腔健康记录失败', res);
        // 使用模拟数据作为后备
        useMockData();
      }
    }).catch(error => {
      // 隐藏加载中
      uni.hideLoading();
      console.error('获取口腔健康记录错误', error);
      
      // 使用模拟数据作为后备
      useMockData();
    });
    */
  } catch (error) {
    console.error('获取口腔健康记录失败', error);
    
    // 使用模拟数据作为后备
    useMockData();
  }
};

// 使用模拟数据作为后备
const useMockData = () => {
  console.log('使用模拟数据作为后备');
  const res = dentalMock.getLatestDentalRecord();
  if (res.success) {
    latestDentalRecord.value = res.record;
  }
};

// 点击其他区域关闭搜索结果
onMounted(() => {
  // 加载搜索历史
  loadSearchHistory();
  
  // 获取最新口腔健康记录
  fetchLatestDentalRecord();
  
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.search-box')) {
      showSearchResults.value = false;
    }
  });
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.address-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  margin-bottom: 20rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 40rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  border: 2rpx solid #e0e0e0;
}

.search-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 36rpx;
  color: #999;
}

.search-dropdown {
  position: absolute;
  top: 85rpx;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400rpx;
  overflow-y: auto;
  border: 1rpx solid #eaeaea;
}

.search-result-item {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}

.search-result-item:active {
  background-color: #f8f8f8;
}

.search-result-item:last-child {
  border-bottom: none;
}

.history-section {
  margin-top: 30rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.history-title {
  font-size: 28rpx;
  color: #666;
}

.clear-history {
  font-size: 24rpx;
  color: #999;
  padding: 5rpx 10rpx;
}

.quick-area-select {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-top: 10rpx;
}

.area-tag {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.area-tag:active {
  background-color: #e0e0e0;
}



.dental-health-section {
  margin-bottom: 30rpx;
}

/* 专科推荐样式 */
.specialty-recommendation-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.specialty-header {
  margin-bottom: 25rpx;
}

.specialty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.specialty-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.analysis-content {
  margin-top: 20rpx;
}

.analysis-summary {
  background-color: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 25rpx;
  border-left: 6rpx solid #4a90e2;
}

.analysis-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}

.specialty-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.specialty-card {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.specialty-card:active {
  transform: scale(0.98);
  background-color: #e9ecef;
}

.specialty-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.icon-text {
  font-size: 36rpx;
}

.specialty-info {
  flex: 1;
  min-width: 0;
}

.specialty-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.specialty-reason {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 8rpx;
}

.specialty-priority {
  font-size: 22rpx;
  color: #4a90e2;
  font-weight: 500;
  display: block;
}

/* 智能推荐提示样式 */
.smart-recommendation-tip {
  margin-bottom: 30rpx;
  text-align: center;
}

.tip-content {
  font-size: 28rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  display: inline-block;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 推荐医生样式 */
.recommended-doctors-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.doctors-header {
  margin-bottom: 25rpx;
}

.doctors-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.doctors-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.doctors-list {
  max-height: 600rpx;
}

.doctor-card {
  display: flex;
  align-items: center;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.doctor-card:active {
  transform: scale(0.98);
  background-color: #f0f1f3;
}

.doctor-card:last-child {
  margin-bottom: 0;
}

.doctor-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-info {
  flex: 1;
  min-width: 0;
}

.doctor-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.doctor-specialty {
  font-size: 24rpx;
  color: #4a90e2;
  margin-bottom: 6rpx;
}

.doctor-hospital {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.doctor-rating {
  display: flex;
  align-items: center;
}

.rating-score {
  font-size: 24rpx;
  font-weight: 600;
  color: #ff6b35;
  margin-right: 4rpx;
}

.rating-text {
  font-size: 20rpx;
  color: #ff6b35;
  margin-right: 8rpx;
}

.rating-count {
  font-size: 20rpx;
  color: #999;
}

.doctor-distance {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 15rpx;
}

.distance-text {
  font-size: 22rpx;
  color: #666;
  background-color: #e8f4fd;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.view-more-section {
  margin-top: 25rpx;
  text-align: center;
}

.view-more-btn {
  background-color: transparent;
  color: #4a90e2;
  font-size: 28rpx;
  border: 2rpx solid #4a90e2;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  transition: all 0.3s ease;
}

.view-more-btn:active {
  background-color: #4a90e2;
  color: #ffffff;
}

/* 反馈按钮样式 - 淡紫色风格 */
.feedback-section {
  margin-top: 20rpx;
  text-align: center;
}

.feedback-btn {
  background-color: #e6e6fa;
  color: #6a5acd;
  font-size: 26rpx;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.feedback-btn:active {
  background-color: #ddd5f3;
  color: #5a4fcf;
  transform: scale(0.98);
}
</style>