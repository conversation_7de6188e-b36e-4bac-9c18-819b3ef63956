# 口腔健康预约系统数据库设计

## 数据库选型建议

建议使用关系型数据库（如MySQL、PostgreSQL）作为主数据库，原因如下：
1. 系统中存在大量关联数据（如用户、预约、评价等）
2. 需要事务支持确保数据一致性（如预约流程）
3. 需要复杂查询支持（如按距离、专科筛选医生）

## 表结构设计

### 1. 用户表 (users)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| user_id | VARCHAR | 32 | 否 | | 主键，用户ID |
| user_type | VARCHAR | 20 | 否 | | 用户类型：patient(患者)、doctor(医生)、admin(管理员) |
| name | VARCHAR | 50 | 否 | | 用户姓名 |
| gender | VARCHAR | 10 | 是 | NULL | 性别：male(男)、female(女) |
| phone | VARCHAR | 20 | 是 | NULL | 手机号码 |
| email | VARCHAR | 100 | 是 | NULL | 邮箱 |
| password | VARCHAR | 100 | 否 | | 密码（加密存储） |
| security_question | VARCHAR | 200 | 是 | NULL | 安全问题 |
| security_answer | VARCHAR | 200 | 是 | NULL | 安全问题答案（加密存储） |
| avatar | VARCHAR | 255 | 是 | NULL | 头像URL |
| created_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- 主键：user_id
- 唯一索引：phone（如果不为NULL）
- 唯一索引：email（如果不为NULL）
- 普通索引：user_type

### 2. 患者信息表 (patient_info)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| patient_id | VARCHAR | 32 | 否 | | 主键，关联users表的user_id |
| latitude | DECIMAL | (10,7) | 是 | NULL | 患者位置纬度 |
| longitude | DECIMAL | (10,7) | 是 | NULL | 患者位置经度 |
| address | VARCHAR | 255 | 是 | NULL | 患者地址描述 |
| updated_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 位置更新时间 |

**索引：**
- 主键：patient_id
- 外键：patient_id 关联 users(user_id)

### 3. 医生信息表 (doctor_info)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| doctor_id | VARCHAR | 32 | 否 | | 主键，关联users表的user_id |
| specialty | VARCHAR | 50 | 否 | | 专科 |
| hospital | VARCHAR | 100 | 否 | | 诊所名称 |
| address | VARCHAR | 255 | 否 | | 诊所地址 |
| latitude | DECIMAL | (10,7) | 否 | | 诊所位置纬度 |
| longitude | DECIMAL | (10,7) | 否 | | 诊所位置经度 |
| introduction | TEXT | | 是 | NULL | 个人简介 |
| working_years | INT | | 是 | NULL | 工作年限 |
| rating | DECIMAL | (2,1) | 否 | 0 | 评分（1-5分） |
| rating_count | INT | | 否 | 0 | 评价数量 |
| created_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- 主键：doctor_id
- 外键：doctor_id 关联 users(user_id)
- 普通索引：specialty
- 空间索引：(latitude, longitude)（如果数据库支持）

### 4. 医生空闲时间表 (doctor_available_slots)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| slot_id | VARCHAR | 32 | 否 | | 主键，时间段ID |
| doctor_id | VARCHAR | 32 | 否 | | 医生ID |
| date | DATE | | 否 | | 日期 |
| start_time | TIME | | 否 | | 开始时间 |
| end_time | TIME | | 否 | | 结束时间 |
| is_booked | BOOLEAN | | 否 | FALSE | 是否已被预约 |
| created_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 创建时间 |

**索引：**
- 主键：slot_id
- 外键：doctor_id 关联 doctor_info(doctor_id)
- 复合索引：(doctor_id, date, is_booked)

### 5. 预约表 (appointments)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| appointment_id | VARCHAR | 32 | 否 | | 主键，预约ID |
| patient_id | VARCHAR | 32 | 否 | | 患者ID |
| doctor_id | VARCHAR | 32 | 否 | | 医生ID |
| slot_id | VARCHAR | 32 | 否 | | 时间段ID |
| appointment_time | DATETIME | | 否 | | 预约时间 |
| duration | INT | | 否 | 30 | 预约时长（分钟） |
| status | VARCHAR | 20 | 否 | 'confirmed' | 状态：confirmed(已确认)、completed(已完成)、cancelled(已取消) |
| note | VARCHAR | 255 | 是 | NULL | 预约备注 |
| cancel_reason | VARCHAR | 255 | 是 | NULL | 取消原因 |
| created_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- 主键：appointment_id
- 外键：patient_id 关联 patient_info(patient_id)
- 外键：doctor_id 关联 doctor_info(doctor_id)
- 外键：slot_id 关联 doctor_available_slots(slot_id)
- 复合索引：(patient_id, status)
- 复合索引：(doctor_id, status)
- 索引：appointment_time

### 6. 评价表 (ratings)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| rating_id | VARCHAR | 32 | 否 | | 主键，评价ID |
| patient_id | VARCHAR | 32 | 否 | | 患者ID |
| doctor_id | VARCHAR | 32 | 否 | | 医生ID |
| appointment_id | VARCHAR | 32 | 否 | | 预约ID |
| score | INT | | 否 | | 评分（1-5） |
| comment | TEXT | | 是 | NULL | 评价内容 |
| created_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 创建时间 |

**索引：**
- 主键：rating_id
- 外键：patient_id 关联 patient_info(patient_id)
- 外键：doctor_id 关联 doctor_info(doctor_id)
- 外键：appointment_id 关联 appointments(appointment_id)
- 唯一索引：appointment_id（一个预约只能评价一次）

### 7. 通知表 (notifications)

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| --- | --- | --- | --- | --- | --- |
| notification_id | VARCHAR | 32 | 否 | | 主键，通知ID |
| user_id | VARCHAR | 32 | 否 | | 接收用户ID |
| type | VARCHAR | 20 | 否 | | 通知类型：appointment(预约相关)、system(系统通知) |
| title | VARCHAR | 100 | 否 | | 通知标题 |
| content | TEXT | | 否 | | 通知内容 |
| is_read | BOOLEAN | | 否 | FALSE | 是否已读 |
| related_id | VARCHAR | 32 | 是 | NULL | 相关ID，如预约ID |
| related_type | VARCHAR | 20 | 是 | NULL | 相关类型 |
| created_at | TIMESTAMP | | 否 | CURRENT_TIMESTAMP | 创建时间 |

**索引：**
- 主键：notification_id
- 外键：user_id 关联 users(user_id)
- 复合索引：(user_id, is_read)
- 复合索引：(user_id, type)
- 索引：related_id

## 数据库关系图

```
+------------+       +---------------+       +------------+
|   users    |------>| patient_info  |       | doctor_info|
+------------+       +---------------+       +------------+
      |                                            |
      |                                            |
      v                                            v
+------------+       +----------------------+      |
|notifications|<------|    appointments     |<-----+
+------------+       +----------------------+
                              |
                              |
                              v
                     +----------------+
                     |    ratings     |
                     +----------------+
```

## 数据库优化建议

1. **分区表**：对于大量数据的表（如预约表、通知表），可以按时间进行分区，提高查询效率。

2. **读写分离**：可以设置主从复制，主库负责写操作，从库负责读操作，提高系统性能。

3. **缓存策略**：对于频繁访问的数据（如医生列表、预约信息），可以使用Redis等缓存系统。

4. **地理位置查询优化**：
   - 使用空间索引（如MySQL的SPATIAL索引）
   - 考虑使用地理位置专用数据库（如MongoDB的地理空间索引）

5. **定期归档**：对于历史数据（如过期预约、已读通知），可以定期归档到历史表中，保持活跃表的数据量较小。 