<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端登录页面原型设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            padding: 15px 0;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .header h1 {
            font-size: 20px;
            color: #333;
            font-weight: 500;
        }
        
        .header p {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        @media (min-width: 768px) {
            .container {
                flex-direction: row;
                align-items: flex-start;
            }
        }
        
        /* 移动端设计部分 */
        .design-container {
            width: 100%;
            max-width: 375px;
            height: 667px;
            background-color: #333;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            margin: 0 auto 30px;
        }
        
        @media (min-width: 768px) {
            .design-container {
                margin: 0 30px 0 0;
                flex-shrink: 0;
            }
        }
        
        .phone-screen {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            background-color: #a8c0ff;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .login-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            width: 100%;
            max-width: 300px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .login-header h2 {
            font-size: 22px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .login-header p {
            font-size: 14px;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 15px;
            position: relative;
        }
        
        .form-group label {
            display: block;
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
            transition: all 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #a8c0ff;
            box-shadow: 0 0 0 2px rgba(168, 192, 255, 0.2);
        }
        
        .form-group .icon {
            position: absolute;
            right: 12px;
            top: 33px;
            color: #999;
            font-size: 16px;
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 13px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .remember-me input[type="checkbox"] {
            appearance: none;
            width: 16px;
            height: 16px;
            border: 1px solid #a8c0ff;
            border-radius: 3px;
            margin-right: 6px;
            position: relative;
            cursor: pointer;
        }
        
        .remember-me input[type="checkbox"]:checked {
            background-color: #a8c0ff;
        }
        
        .remember-me input[type="checkbox"]:checked::before {
            content: "✓";
            position: absolute;
            color: white;
            font-size: 11px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .forgot-password {
            color: #a8c0ff;
            text-decoration: none;
        }
        
        .login-button {
            width: 100%;
            padding: 12px;
            background-color: #a8c0ff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .login-button:hover {
            background-color: #97b1f8;
        }
        
        .signup-link {
            text-align: center;
            font-size: 13px;
            color: #666;
        }
        
        .signup-link a {
            color: #a8c0ff;
            text-decoration: none;
            font-weight: 500;
        }
        
        /* 设计说明部分 */
        .design-explanation {
            flex: 1;
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }
        
        .design-explanation h2 {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #a8c0ff;
        }
        
        .explanation-section {
            margin-bottom: 20px;
        }
        
        .explanation-section h3 {
            font-size: 16px;
            color: #a8c0ff;
            margin-bottom: 10px;
        }
        
        .explanation-section p {
            font-size: 14px;
            line-height: 1.5;
            color: #666;
            margin-bottom: 10px;
        }
        
        .color-palette {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .color-box {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding-bottom: 5px;
            font-size: 10px;
            color: white;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .primary-color {
            background-color: #a8c0ff;
        }
        
        .secondary-color {
            background-color: #8e99f3;
        }
        
        .text-dark {
            background-color: #333;
        }
        
        .text-medium {
            background-color: #666;
        }
        
        .text-light {
            background-color: #999;
            color: #333;
        }
        
        .bg-color {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #eee;
        }
        
        .features-list {
            list-style-type: none;
            margin-left: 0;
            padding-left: 0;
        }
        
        .features-list li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .features-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #a8c0ff;
            font-weight: bold;
        }
        
        @media (max-width: 767px) {
            .design-explanation {
                margin-top: 20px;
            }
            
            .color-box {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>移动端登录页面原型设计</h1>
        <p>简洁美观的移动端登录界面</p>
    </div>
    
    <div class="container">
        <!-- 移动端设计展示 -->
        <div class="design-container">
            <div class="phone-screen">
                <div class="login-container">
                    <div class="login-card">
                        <div class="login-header">
                            <h2>欢迎回来</h2>
                            <p>请登录您的账号</p>
                        </div>
                        
                        <form>
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" id="username" placeholder="请输入手机号或邮箱">
                                <span class="icon">👤</span>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">密码</label>
                                <input type="password" id="password" placeholder="请输入密码">
                                <span class="icon">🔒</span>
                            </div>
                            
                            <div class="form-options">
                                <div class="remember-me">
                                    <input type="checkbox" id="remember">
                                    <label for="remember">记住我</label>
                                </div>
                                <a href="#" class="forgot-password">忘记密码?</a>
                            </div>
                            
                            <button type="button" class="login-button">登 录</button>
                            
                            <div class="signup-link">
                                还没有账号？ <a href="#">立即注册</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 设计说明部分 -->
        <div class="design-explanation">
            <h2>设计说明</h2>
            
            <div class="explanation-section">
                <h3>色彩方案</h3>
                <p>采用浅蓝色为主色调，给用户带来轻松、友好的视觉体验。配色简洁明快，符合现代移动端设计趋势。</p>
                <div class="color-palette">
                    <div class="color-box primary-color">#a8c0ff</div>
                    <div class="color-box secondary-color">#8e99f3</div>
                    <div class="color-box text-dark">#333</div>
                    <div class="color-box text-medium">#666</div>
                    <div class="color-box text-light">#999</div>
                    <div class="color-box bg-color">#f5f5f5</div>
                </div>
            </div>
            
            <div class="explanation-section">
                <h3>视觉元素</h3>
                <ul class="features-list">
                    <li>磨砂效果：卡片使用半透明背景增强视觉层次感</li>
                    <li>圆角设计：所有元素采用圆角设计，增强友好感</li>
                    <li>阴影效果：适当的阴影增强界面层次感和立体感</li>
                    <li>图标指示：输入框右侧添加图标提升可用性</li>
                    <li>紧凑布局：元素间距适中，整体布局紧凑美观</li>
                </ul>
            </div>
            
            <div class="explanation-section">
                <h3>交互设计</h3>
                <ul class="features-list">
                    <li>可点击元素：所有交互元素都可以点击</li>
                    <li>浮动效果：输入框获取焦点时边框高亮</li>
                    <li>按钮反馈：登录按钮具有悬停效果</li>
                    <li>选择状态：记住我复选框有明确的选中状态</li>
                    <li>链接跳转：忘记密码和注册链接可点击</li>
                </ul>
            </div>
            
            <div class="explanation-section">
                <h3>功能特点</h3>
                <ul class="features-list">
                    <li>支持手机号或邮箱登录</li>
                    <li>记住登录状态选项</li>
                    <li>忘记密码入口</li>
                    <li>新用户注册入口</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 输入框焦点效果
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.borderColor = '#a8c0ff';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.borderColor = '';
            });
        });
        
        // 登录按钮点击效果
        const loginButton = document.querySelector('.login-button');
        loginButton.addEventListener('click', function() {
            alert('登录功能模拟：登录请求已发送！');
        });
        
        // 忘记密码点击效果
        const forgotPassword = document.querySelector('.forgot-password');
        forgotPassword.addEventListener('click', function(e) {
            e.preventDefault();
            alert('忘记密码功能：密码重置链接将发送到您的邮箱/手机');
        });
        
        // 注册链接点击效果
        const signupLink = document.querySelector('.signup-link a');
        signupLink.addEventListener('click', function(e) {
            e.preventDefault();
            alert('注册功能：即将跳转到注册页面');
        });
        
        // 记住我复选框点击效果
        const rememberCheckbox = document.getElementById('remember');
        rememberCheckbox.addEventListener('change', function() {
            if(this.checked) {
                console.log('记住登录状态：开启');
            } else {
                console.log('记住登录状态：关闭');
            }
        });
    </script>
</body>
</html> 