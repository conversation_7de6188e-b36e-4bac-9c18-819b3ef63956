<template>
  <view class="container">
    <view class="header">
      <text class="title">医生信息管理</text>
      <text class="subtitle">更新个人资料和专业信息</text>
    </view>
    
    <view class="content">
      <!-- 个人资料卡片 -->
      <view class="profile-card">
        <view class="avatar-section">
          <view class="avatar">
            <text class="avatar-text">{{ doctorInfo.name ? doctorInfo.name.substring(0, 1) : '医' }}</text>
          </view>
          <view class="upload-btn" @tap="uploadAvatar">
            <text class="upload-text">更换头像</text>
          </view>
        </view>
        
        <view class="info-section">
          <view class="info-item">
            <text class="info-label">姓名</text>
            <input
              v-if="isEditing"
              class="info-input"
              v-model="doctorInfo.name"
              placeholder="请输入姓名"
            />
            <view v-else class="info-display">{{ doctorInfo.name || '未设置' }}</view>
          </view>

          <view class="info-item">
            <text class="info-label">性别</text>
            <picker
              v-if="isEditing"
              class="info-picker"
              :range="genderOptions"
              @change="onGenderChange"
            >
              <view class="picker-value">{{ doctorInfo.gender || '请选择' }}</view>
            </picker>
            <view v-else class="info-display">{{ doctorInfo.gender || '未设置' }}</view>
          </view>

          <view class="info-item">
            <text class="info-label">手机号</text>
            <view class="info-display">{{ doctorInfo.phone || '未设置' }}</view>
          </view>

          <view class="info-item">
            <text class="info-label">邮箱</text>
            <input
              v-if="isEditing"
              class="info-input"
              v-model="doctorInfo.email"
              placeholder="请输入邮箱"
            />
            <view v-else class="info-display">{{ doctorInfo.email || '未设置' }}</view>
          </view>
        </view>
      </view>
      
      <!-- 专业信息卡片 -->
      <view class="profile-card">
        <view class="card-header">
          <text class="card-title">专业信息</text>
        </view>
        
        <view class="info-section professional-info">
          <view class="pro-info-row">
            <text class="pro-info-label">诊所名称</text>
            <input
              v-if="isEditing"
              class="pro-info-input"
              v-model="doctorInfo.clinicName"
              placeholder="请输入诊所名称"
            />
            <view v-else class="info-display">{{ doctorInfo.clinicName || '未设置' }}</view>
          </view>

          <view class="pro-info-row">
            <text class="pro-info-label">诊所位置</text>
            <input
              v-if="isEditing"
              class="pro-info-input"
              v-model="doctorInfo.clinicLocation"
              placeholder="请输入诊所位置"
            />
            <view v-else class="info-display">{{ doctorInfo.clinicLocation || '未设置' }}</view>
          </view>

          <view class="pro-info-row">
            <text class="pro-info-label">擅长专科</text>
            <view
              v-if="isEditing"
              class="specialty-selector"
              @tap="showSpecialtyPicker"
            >
              <view class="selected-specialties">
                <view v-if="selectedSpecialties.length === 0" class="placeholder">请选择1-2个专科</view>
                <view v-else class="specialty-tags">
                  <view
                    v-for="(specialty, index) in selectedSpecialties"
                    :key="index"
                    class="specialty-tag"
                  >
                    {{ specialty }}
                    <text class="remove-tag" @tap.stop="removeSpecialty(index)">×</text>
                  </view>
                </view>
              </view>
              <text class="selector-arrow">></text>
            </view>
            <view v-else class="specialty-display">
              <view v-if="selectedSpecialties.length === 0" class="info-display">未设置</view>
              <view v-else class="specialty-tags readonly">
                <view
                  v-for="(specialty, index) in selectedSpecialties"
                  :key="index"
                  class="specialty-tag readonly"
                >
                  {{ specialty }}
                </view>
              </view>
            </view>
          </view>

          <view class="pro-info-row">
            <text class="pro-info-label">就医时长</text>
            <view v-if="isEditing" class="experience-input-wrapper">
              <input
                class="pro-info-input experience-input"
                v-model="doctorInfo.experience"
                placeholder="请输入年数"
                type="number"
                maxlength="2"
              />
              <text class="experience-unit">年</text>
            </view>
            <view v-else class="info-display">{{ doctorInfo.experience ? doctorInfo.experience + '年' : '未设置' }}</view>
          </view>

          <view class="pro-info-row">
            <text class="pro-info-label">职务</text>
            <input
              v-if="isEditing"
              class="pro-info-input"
              v-model="doctorInfo.position"
              placeholder="请输入职务"
            />
            <view v-else class="info-display">{{ doctorInfo.position || '未设置' }}</view>
          </view>

          <view class="pro-info-row">
            <text class="pro-info-label">个人简介</text>
            <textarea
              v-if="isEditing"
              class="pro-info-textarea"
              v-model="doctorInfo.introduction"
              placeholder="请输入您的个人简介"
            />
            <view v-else class="info-display multi-line">{{ doctorInfo.introduction || '未设置' }}</view>
          </view>
        </view>
      </view>
      
      <!-- 账号安全卡片 -->
      <view class="profile-card">
        <view class="card-header">
          <text class="card-title">账号安全</text>
        </view>
        
        <view class="info-section">
          <view class="action-item" @tap="changePassword">
            <text class="action-text">修改密码</text>
            <text class="action-icon">></text>
          </view>
          
          <view class="action-item" @tap="bindPhone">
            <text class="action-text">绑定手机号</text>
            <text class="action-text-status">{{ doctorInfo.phone ? '已绑定' : '未绑定' }}</text>
            <text class="action-icon">></text>
          </view>
        </view>
      </view>
      
      <!-- 编辑/保存按钮 -->
      <button class="save-button" @tap="toggleEdit">
        {{ isEditing ? '保存修改' : '点击修改' }}
      </button>
    </view>

    <!-- 专科选择弹窗 -->
    <view v-if="showSpecialtyModal" class="modal-overlay" @tap="hideSpecialtyPicker">
      <view class="specialty-modal" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">选择擅长专科</text>
          <text class="modal-subtitle">最多选择2个专科</text>
        </view>

        <view class="specialty-list">
          <view
            v-for="(specialty, index) in availableSpecialties"
            :key="index"
            class="specialty-option"
            :class="{
              'selected': tempSelectedSpecialties.includes(specialty),
              'disabled': !tempSelectedSpecialties.includes(specialty) && tempSelectedSpecialties.length >= 2
            }"
            @tap="toggleSpecialty(specialty)"
          >
            <text class="specialty-name">{{ specialty }}</text>
            <text v-if="tempSelectedSpecialties.includes(specialty)" class="check-icon">✓</text>
          </view>
        </view>

        <view class="modal-actions">
          <button class="modal-button cancel" @tap="hideSpecialtyPicker">取消</button>
          <button class="modal-button confirm" @tap="confirmSpecialtySelection">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 医生信息
const doctorInfo = ref({
  id: '',
  name: '',
  gender: '',
  phone: '',
  email: '',
  clinicName: '',    // 诊所名称
  clinicLocation: '',
  specialization: '',
  introduction: '',
  experience: '', // 就医时长（年）
  position: ''    // 职务
});

// 编辑状态
const isEditing = ref(false);

// 选项数据
const genderOptions = ['男', '女'];

// 专科相关数据
const availableSpecialties = [
  '口腔内科',
  '牙周病科',
  '口腔正畸科',
  '口腔外科',
  '口腔种植科',
  '口腔修复科',
  '牙体牙髓科',
  '口腔颌面外科',
  '儿童口腔科',
  '口腔预防科'
];

const selectedSpecialties = ref([]);
const tempSelectedSpecialties = ref([]);
const showSpecialtyModal = ref(false);

// 页面加载时
onMounted(() => {
  // 获取医生信息
  try {
    const doctor = uni.getStorageSync('doctorInfo');

    if (doctor && Object.keys(doctor).length > 0) {
      // 合并存储的信息到当前数据
      doctorInfo.value = { ...doctor };

      // 如果有专科信息，解析为数组
      if (doctor.specialization) {
        if (Array.isArray(doctor.specialization)) {
          selectedSpecialties.value = [...doctor.specialization];
        } else {
          // 如果是字符串，尝试分割
          selectedSpecialties.value = doctor.specialization.includes(',')
            ? doctor.specialization.split(',').map(s => s.trim())
            : [doctor.specialization];
        }
      }
    } else {
      // 如果没有存储的医生信息，使用模拟数据并保存
      const defaultDoctorInfo = {
        id: 'D10001',
        name: '李医生',
        gender: '男',
        phone: '13900139000',
        email: '<EMAIL>',
        clinicName: '健康口腔诊所',
        clinicLocation: '北京市朝阳区健康路88号',
        specialization: ['口腔正畸科'],
        introduction: '从事口腔医学工作10年，擅长各类牙齿矫正和美容修复。',
        experience: '10',
        position: '主治医师'
      };

      // 设置数据
      doctorInfo.value = { ...defaultDoctorInfo };
      selectedSpecialties.value = [...defaultDoctorInfo.specialization];

      // 保存到本地存储
      uni.setStorageSync('doctorInfo', defaultDoctorInfo);
    }
  } catch (e) {
    console.error('获取医生信息失败', e);
  }
});

// 上传头像
const uploadAvatar = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const tempFilePaths = res.tempFilePaths;
      
      // 实际应用中应该上传到服务器
      uni.showToast({
        title: '头像上传成功',
        icon: 'success'
      });
      
      // 这里应该更新头像URL
      // doctorInfo.value.avatar = uploadedImageUrl;
    }
  });
};

// 性别选择变化
const onGenderChange = (e) => {
  const index = e.detail.value;
  doctorInfo.value.gender = genderOptions[index];
};

// 修改密码
const changePassword = () => {
  uni.navigateTo({
    url: '/pages/change-password/index'
  });
};

// 绑定手机号
const bindPhone = () => {
  if (doctorInfo.value.phone) {
    uni.showModal({
      title: '提示',
      content: '您已绑定手机号，是否需要更换？',
      success: (res) => {
        if (res.confirm) {
          // 跳转到绑定手机号页面
          uni.navigateTo({
            url: '/pages/bind-phone/index'
          });
        }
      }
    });
  } else {
    // 跳转到绑定手机号页面
    uni.navigateTo({
      url: '/pages/bind-phone/index'
    });
  }
};

// 专科选择相关方法
const showSpecialtyPicker = () => {
  if (!isEditing.value) return; // 只在编辑状态下可用
  tempSelectedSpecialties.value = [...selectedSpecialties.value];
  showSpecialtyModal.value = true;
};

const hideSpecialtyPicker = () => {
  showSpecialtyModal.value = false;
  tempSelectedSpecialties.value = [];
};

const toggleSpecialty = (specialty) => {
  const index = tempSelectedSpecialties.value.indexOf(specialty);
  if (index > -1) {
    // 如果已选择，则移除
    tempSelectedSpecialties.value.splice(index, 1);
  } else {
    // 如果未选择且未达到上限，则添加
    if (tempSelectedSpecialties.value.length < 2) {
      tempSelectedSpecialties.value.push(specialty);
    } else {
      uni.showToast({
        title: '最多只能选择2个专科',
        icon: 'none'
      });
    }
  }
};

const removeSpecialty = (index) => {
  selectedSpecialties.value.splice(index, 1);
};

const confirmSpecialtySelection = () => {
  selectedSpecialties.value = [...tempSelectedSpecialties.value];
  hideSpecialtyPicker();

  uni.showToast({
    title: '专科选择已更新',
    icon: 'success'
  });
};

// 编辑状态切换
const toggleEdit = () => {
  if (isEditing.value) {
    // 当前是编辑状态，点击保存
    saveProfile();
  } else {
    // 当前是查看状态，切换到编辑状态
    isEditing.value = true;
  }
};

// 保存个人信息
const saveProfile = () => {
  // 表单验证
  if (!doctorInfo.value.name) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none'
    });
    return;
  }

  if (!doctorInfo.value.clinicName) {
    uni.showToast({
      title: '请输入诊所名称',
      icon: 'none'
    });
    return;
  }

  if (!doctorInfo.value.clinicLocation) {
    uni.showToast({
      title: '请输入诊所位置',
      icon: 'none'
    });
    return;
  }

  if (selectedSpecialties.value.length === 0) {
    uni.showToast({
      title: '请选择擅长专科',
      icon: 'none'
    });
    return;
  }

  // 保存到本地存储
  try {
    const saveData = {
      ...doctorInfo.value,
      specialization: selectedSpecialties.value
    };

    uni.setStorageSync('doctorInfo', saveData);

    // 保存成功后退出编辑状态
    isEditing.value = false;

    // 实际应用中应该调用API保存到服务器
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
  } catch (e) {
    console.error('保存医生信息失败', e);
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 40rpx 30rpx;
  color: #fff;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.content {
  flex: 1;
  padding: 30rpx;
}

/* 资料卡片样式 */
.profile-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 头像部分样式 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  background-color: #8e99f3;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar-text {
  font-size: 80rpx;
  color: #fff;
  font-weight: bold;
}

.upload-btn {
  background-color: rgba(142, 153, 243, 0.1);
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #8e99f3;
}

/* 信息部分样式 */
.info-section {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15rpx;
}

.info-item {
  width: calc(50% - 30rpx);
  margin: 0 15rpx 30rpx;
}

.info-item.full {
  width: calc(100% - 30rpx);
}

.info-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.info-display {
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #666;
  width: 100%;
  box-sizing: border-box;
}

.multi-line {
  white-space: pre-wrap;
  line-height: 1.6;
  min-height: 80rpx;
}

.specialty-display {
  width: 100%;
}

.specialty-tags.readonly {
  gap: 10rpx;
}

.specialty-tag.readonly {
  background-color: #e8f0ff;
  color: #8e99f3;
  border: 1rpx solid #8e99f3;
}

.info-input {
  background-color: #fff;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  border: 2rpx solid #4a90e2;
  color: #333;
  height: 80rpx;
  line-height: 50rpx;
}

.info-picker {
  background-color: #fff;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  width: 100%;
  box-sizing: border-box;
  border: 2rpx solid #4a90e2;
  height: 80rpx;
  display: flex;
  align-items: center;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 50rpx;
}

.info-textarea {
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
}

/* 专业信息样式 */
.professional-info {
  display: flex;
  flex-direction: column;
  margin: 0;
}

.pro-info-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
  width: 100%;
}

.pro-info-label {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.pro-info-input {
  background-color: #fff;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  border: 2rpx solid #4a90e2;
  color: #333;
  height: 80rpx;
  line-height: 50rpx;
}

/* 就医时长输入框样式 */
.experience-input-wrapper {
  display: flex;
  align-items: center;
  gap: 15rpx;
  width: 100%;
}

.experience-input {
  flex: 1;
  width: auto !important;
  margin-right: 0 !important;
}

.experience-unit {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  line-height: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
}

.pro-info-textarea {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  width: 100%;
  height: 200rpx;
  box-sizing: border-box;
  border: 2rpx solid #4a90e2;
  color: #333;
  line-height: 1.5;
}

/* 操作项样式 */
.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.action-item:last-child {
  border-bottom: none;
}

.action-text {
  font-size: 30rpx;
  color: #333;
}

.action-text-status {
  font-size: 28rpx;
  color: #999;
  margin-left: auto;
  margin-right: 20rpx;
}

.action-icon {
  font-size: 30rpx;
  color: #999;
}

/* 保存按钮样式 */
.save-button {
  background-color: #8e99f3;
  color: #fff;
  font-size: 32rpx;
  padding: 25rpx 0;
  border-radius: 10rpx;
  margin-top: 20rpx;
  margin-bottom: 50rpx;
  border: none;
}

/* 专科选择样式 */
.specialty-selector {
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.selected-specialties {
  flex: 1;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

.specialty-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.specialty-tag {
  background-color: #8e99f3;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.remove-tag {
  font-size: 20rpx;
  font-weight: bold;
  cursor: pointer;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 专科选择弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.specialty-modal {
  background-color: #fff;
  border-radius: 20rpx;
  width: 80%;
  max-height: 70%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.modal-subtitle {
  font-size: 24rpx;
  color: #666;
}

.specialty-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.specialty-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  background-color: #f8f9fa;
}

.specialty-option.selected {
  background-color: #e8f0ff;
  border: 2rpx solid #8e99f3;
}

.specialty-option.disabled {
  opacity: 0.5;
}

.specialty-name {
  font-size: 28rpx;
  color: #333;
}

.check-icon {
  font-size: 24rpx;
  color: #8e99f3;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  padding: 20rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-button {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.modal-button.cancel {
  background-color: #f5f7fa;
  color: #666;
}

.modal-button.confirm {
  background-color: #8e99f3;
  color: #fff;
}
</style>