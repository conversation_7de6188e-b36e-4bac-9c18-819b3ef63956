<template>
  <view class="container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">个人信息</text>
    </view>
    
    <!-- 基本信息卡片 -->
    <view class="section">
      <view class="section-title">基本信息</view>
      <view class="info-card">
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">{{ userInfo.name || '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">电话号码</text>
          <text class="info-value">{{ userInfo.phone || '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">{{ userInfo.gender || '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">年龄</text>
          <text class="info-value">{{ userInfo.age || '未设置' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 口腔健康卡片 -->
    <view class="section">
      <view class="section-title">口腔健康状况</view>
      <dental-health-card v-if="dentalRecord" :record="dentalRecord" />
      <view v-else class="no-data">
        <text>暂无口腔健康数据</text>
      </view>
    </view>
    
    <!-- 健康趋势图 -->
    <view class="section">
      <view class="section-title">健康趋势</view>
      <view v-if="dentalRecords.length > 1" class="chart-container">
        <view class="chart-header">
          <text class="chart-title">口腔健康评分趋势</text>
        </view>
        <scroll-view scroll-x class="chart-scroll-view" show-scrollbar="false" enhanced="true">
          <view class="chart" :style="{ width: chartWidth }">
            <view class="chart-y-axis">
              <text v-for="(value, index) in yAxisValues" :key="index" class="y-axis-label">{{ value }}</text>
            </view>
            <view class="chart-content">
              <view class="chart-grid">
                <view v-for="(_, index) in yAxisValues" :key="index" class="grid-line"></view>
              </view>
              
              <!-- SVG曲线图 -->
              <view class="svg-container">
                <svg :width="svgWidth" :height="svgHeight">
                  <!-- 绘制曲线 -->
                  <path :d="pathD" fill="none" :stroke="chartLineColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                  
                  <!-- 绘制点 -->
                  <template v-for="(point, index) in svgPoints" :key="index">
                    <circle 
                      :cx="point.x" 
                      :cy="point.y" 
                      r="6" 
                      :fill="getScoreColor(chartPoints[index])" 
                    />
                    <text 
                      :x="point.x" 
                      :y="point.y - 10" 
                      text-anchor="middle" 
                      font-size="12" 
                      fill="#666"
                    >{{ chartPoints[index] }}</text>
                  </template>
                </svg>
              </view>
            </view>
            <view class="chart-x-axis">
              <text 
                v-for="(date, index) in chartDates" 
                :key="index" 
                class="x-axis-label"
                :style="{ left: `${index * (pointSpacing)}px` }"
              >
                {{ formatShortDate(date) }}
              </text>
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-else class="no-data">
        <text>暂无足够的历史数据生成趋势图</text>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="primary-btn" @tap="showEditPopup">编辑信息</button>
      <button class="secondary-btn" @tap="goBack">返回</button>
    </view>
    
    <!-- 编辑信息弹窗 -->
    <view class="popup-mask" v-if="showPopup" @tap="closePopup"></view>
    <view class="popup-content" v-if="showPopup">
      <view class="popup-header">
        <text class="popup-title">编辑信息</text>
        <text class="popup-close" @tap="closePopup">×</text>
      </view>
      
      <view class="popup-body">
        <!-- 姓名 -->
        <view class="popup-form-item">
          <text class="popup-form-label">姓名</text>
          <input class="popup-form-input" v-model="editForm.name" placeholder="请输入姓名" />
        </view>
        
        <!-- 性别 -->
        <view class="popup-form-item">
          <text class="popup-form-label">性别</text>
          <view class="popup-gender-options">
            <view 
              class="popup-gender-option" 
              :class="{ active: editForm.gender === '男' }"
              @tap="editForm.gender = '男'"
            >
              <text>男</text>
            </view>
            <view 
              class="popup-gender-option" 
              :class="{ active: editForm.gender === '女' }"
              @tap="editForm.gender = '女'"
            >
              <text>女</text>
            </view>
          </view>
        </view>
        
        <!-- 年龄 -->
        <view class="popup-form-item">
          <text class="popup-form-label">年龄</text>
          <input class="popup-form-input" v-model="editForm.age" type="number" placeholder="请输入年龄" />
        </view>
      </view>
      
      <view class="popup-footer">
        <button class="popup-confirm-btn" @tap="saveUserInfo">确认信息</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';
import { getUserInfo as getStoredUserInfo, saveUserInfo as storeUserInfo } from '@/utils/storage';
import { dentalApi } from '@/api';
import dentalMock from '@/mock/dental';

// 获取全局API
const { proxy } = getCurrentInstance();
const { $api } = proxy;

// 用户信息
const userInfo = ref({
  name: '',
  phone: '',
  gender: '',
  age: ''
});

// 编辑表单
const editForm = ref({
  name: '',
  gender: '男',
  age: ''
});

// 弹窗控制
const showPopup = ref(false);

// 口腔健康记录
const dentalRecord = ref(null);
const dentalRecords = ref([]);

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 显示加载
    uni.showLoading({
      title: '加载中...'
    });
    
    // 获取本地存储的用户信息
    const storedUserInfo = getStoredUserInfo();
    if (storedUserInfo) {
      userInfo.value = {
        name: storedUserInfo.name || '',
        phone: storedUserInfo.phone || '',
        gender: storedUserInfo.gender || '',
        age: storedUserInfo.age || ''
      };
    }
    
    // 从服务器获取完整的用户信息
    try {
      const result = await $api.patient.getPatientInfo();
      if (result && result.data) {
        userInfo.value = {
          ...userInfo.value,
          ...result.data
        };
      }
    } catch (error) {
      console.error('获取服务器用户信息失败', error);
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 获取口腔健康记录
const fetchDentalRecords = async () => {
  try {
    // 使用实际API接口
    const res = await dentalApi.getDentalRecords();
    if (res.success && res.records && res.records.length > 0) {
      dentalRecords.value = res.records;
      dentalRecord.value = res.records[0]; // 最新的记录
    } else {
      console.error('获取口腔健康记录失败', res);
      // 使用模拟数据
      useMockData();
    }
  } catch (error) {
    console.error('获取口腔健康记录错误', error);
    // 使用模拟数据
    useMockData();
  }
};

// 使用模拟数据
const useMockData = () => {
  console.log('使用模拟数据');
  const res = dentalMock.getDentalRecords();
  if (res.success && res.records.length > 0) {
    dentalRecords.value = res.records;
    dentalRecord.value = res.records[0];
  } else {
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
  }
};

// 图表相关计算
const chartPoints = computed(() => {
  return dentalRecords.value.map(record => record.health_score).reverse();
});

const chartDates = computed(() => {
  return dentalRecords.value.map(record => record.created_at).reverse();
});

const yAxisValues = [0, 20, 40, 60, 80, 100];

// 图表尺寸和间距
const pointSpacing = 100; // 每个点之间的间距，单位像素
const chartWidth = computed(() => {
  return `${chartPoints.value.length * pointSpacing + 60}px`; // 60px是y轴宽度
});

// SVG相关计算
const svgWidth = computed(() => chartPoints.value.length * pointSpacing);
const svgHeight = 300;

const svgPoints = computed(() => {
  return chartPoints.value.map((point, index) => {
    return {
      x: index * pointSpacing + 30, // 30px是为了让点不要太靠近y轴
      y: svgHeight - (point / 100) * svgHeight // 将分数转换为y坐标
    };
  });
});

// 生成SVG路径
const pathD = computed(() => {
  if (svgPoints.value.length < 2) return '';
  
  let path = `M ${svgPoints.value[0].x},${svgPoints.value[0].y}`;
  
  // 使用贝塞尔曲线连接点
  for (let i = 0; i < svgPoints.value.length - 1; i++) {
    const current = svgPoints.value[i];
    const next = svgPoints.value[i + 1];
    
    // 控制点，使曲线更平滑
    const cp1x = current.x + (next.x - current.x) / 2;
    const cp1y = current.y;
    const cp2x = current.x + (next.x - current.x) / 2;
    const cp2y = next.y;
    
    path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${next.x},${next.y}`;
  }
  
  return path;
});

// 图表线条颜色
const chartLineColor = computed(() => {
  // 根据平均分数决定颜色
  const avgScore = chartPoints.value.reduce((sum, score) => sum + score, 0) / chartPoints.value.length;
  return getScoreColor(avgScore);
});

const formatShortDate = (dateString) => {
  if (!dateString) return '';
  const date = dateString.split(' ')[0];
  const parts = date.split('-');
  return `${parts[1]}/${parts[2]}`;
};

const getScoreColor = (score) => {
  if (score >= 85) return '#4cd964'; // 好
  if (score >= 70) return '#ffcc00'; // 中
  return '#ff3b30'; // 差
};

// 显示编辑弹窗
const showEditPopup = () => {
  // 初始化编辑表单数据
  editForm.value = {
    name: userInfo.value.name || '',
    gender: userInfo.value.gender || '男',
    age: userInfo.value.age || ''
  };
  
  // 显示弹窗
  showPopup.value = true;
};

// 关闭弹窗
const closePopup = () => {
  showPopup.value = false;
};

// 保存用户信息
const saveUserInfo = async () => {
  // 表单验证
  if (!editForm.value.name) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none'
    });
    return;
  }
  
  try {
    uni.showLoading({
      title: '保存中...'
    });
    
    // 更新本地用户信息
    const updatedUserInfo = {
      ...userInfo.value,
      name: editForm.value.name,
      gender: editForm.value.gender,
      age: editForm.value.age
    };
    
    // 保存到本地存储
    storeUserInfo(updatedUserInfo);
    
    // 更新当前显示的用户信息
    userInfo.value = updatedUserInfo;
    
    // 尝试更新到服务器
    try {
      await $api.patient.updatePatientInfo(updatedUserInfo);
    } catch (error) {
      console.error('更新服务器用户信息失败', error);
      // 即使服务器更新失败，我们也保留本地更新的结果
    }
    
    uni.hideLoading();
    
    // 显示成功提示
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000
    });
    
    // 关闭弹窗
    closePopup();
    
  } catch (error) {
    uni.hideLoading();
    console.error('保存用户信息失败', error);
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面加载时
onMounted(() => {
  fetchUserInfo();
  fetchDentalRecords();
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.page-header {
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #4a5b8c;
  text-align: center;
  display: block;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 0;
  border-radius: 10rpx;
  margin: 0 auto 20rpx;
  width: 80%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.3);
}

.section {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #4a90e2;
  padding-left: 15rpx;
}

.info-card {
  border-radius: 10rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
  font-size: 28rpx;
}

.info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.no-data {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.chart-container {
  padding: 20rpx 0;
}

.chart-header {
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  display: block;
}

.chart-scroll-view {
  width: 100%;
}

.chart {
  height: 400rpx;
  position: relative;
  padding: 20rpx 0 60rpx 60rpx;
}

.chart-y-axis {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 40rpx;
  width: 60rpx;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
}

.y-axis-label {
  font-size: 20rpx;
  color: #999;
  text-align: right;
  padding-right: 10rpx;
}

.chart-content {
  height: 300px;
  position: relative;
  margin-right: 20rpx;
}

.chart-grid {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
}

.grid-line {
  width: 100%;
  height: 1rpx;
  background-color: #eee;
}

.svg-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.chart-x-axis {
  position: absolute;
  left: 60rpx;
  right: 0;
  bottom: 0;
  height: 40rpx;
}

.x-axis-label {
  position: absolute;
  transform: translateX(-50%);
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  gap: 20rpx;
}

.primary-btn {
  flex: 1;
  height: 90rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.secondary-btn {
  flex: 1;
  height: 90rpx;
  background-color: #f0f0f0;
  color: #666;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
}

.primary-btn:active,
.secondary-btn:active {
  transform: scale(0.98);
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.popup-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.popup-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
}

.popup-body {
  padding: 30rpx;
}

.popup-form-item {
  margin-bottom: 30rpx;
}

.popup-form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.popup-form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.popup-gender-options {
  display: flex;
  gap: 20rpx;
}

.popup-gender-option {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  
  &.active {
    background-color: #4a90e2;
    color: #fff;
    border-color: #4a90e2;
  }
}

.popup-footer {
  padding: 20rpx 30rpx 40rpx;
}

.popup-confirm-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4a90e2;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 