import {
	createSSRApp
} from "vue";
import App from "./App.vue";
import api from './api';
import { setupPermissionGuard } from './utils/permission';
import DentalHealthCard from './components/DentalHealthCard.vue';

// 初始化权限控制
setupPermissionGuard();

export function createApp() {
	const app = createSSRApp(App);
	
	// 全局挂载API
	app.config.globalProperties.$api = api;
	
	// 全局注册组件
	app.component('dental-health-card', DentalHealthCard);
	
	return {
		app,
	};
}
