/**
 * 医生相关API
 */
import { get, put, post, del } from '@/utils/request';
import { getUserId } from '@/utils/storage';

/**
 * 获取医生个人信息
 * @param {string} doctorId - 医生ID，不传则获取当前登录医生
 * @returns {Promise} - 医生信息
 */
export const getDoctorInfo = (doctorId) => {
  const id = doctorId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供医生ID'));
  }
  return get(`/doctors/${id}`);
};

/**
 * 更新医生个人信息
 * @param {string} doctorId - 医生ID，不传则更新当前登录医生
 * @param {Object} data - 医生信息
 * @returns {Promise} - 更新结果
 */
export const updateDoctorInfo = (doctorId, data) => {
  const id = doctorId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供医生ID'));
  }
  return put(`/doctors/${id}`, data);
};

/**
 * 获取医生空闲时间列表
 * @param {string} doctorId - 医生ID，不传则获取当前登录医生
 * @param {Object} params - 查询参数
 * @returns {Promise} - 空闲时间列表
 */
export const getDoctorAvailableTimes = (doctorId, params) => {
  const id = doctorId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供医生ID'));
  }
  return get(`/doctors/${id}/available-times`, params);
};

/**
 * 添加医生空闲时间
 * @param {string} doctorId - 医生ID，不传则为当前登录医生
 * @param {Object} data - 空闲时间信息
 * @returns {Promise} - 添加结果
 */
export const addDoctorAvailableTime = (doctorId, data) => {
  const id = doctorId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供医生ID'));
  }
  return post(`/doctors/${id}/available-times`, data);
};

/**
 * 删除医生空闲时间
 * @param {string} doctorId - 医生ID，不传则为当前登录医生
 * @param {string} timeId - 空闲时间ID
 * @returns {Promise} - 删除结果
 */
export const deleteDoctorAvailableTime = (doctorId, timeId) => {
  const id = doctorId || getUserId();
  if (!id || !timeId) {
    return Promise.reject(new Error('未提供医生ID或时间ID'));
  }
  return del(`/doctors/${id}/available-times/${timeId}`);
};

/**
 * 获取医生预约列表
 * @param {string} doctorId - 医生ID，不传则获取当前登录医生
 * @param {Object} params - 查询参数
 * @returns {Promise} - 预约列表
 */
export const getDoctorAppointments = (doctorId, params = {}) => {
  const id = doctorId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供医生ID'));
  }
  return get(`/doctors/${id}/appointments`, params);
};

/**
 * 获取医生评分列表
 * @param {string} doctorId - 医生ID，不传则获取当前登录医生
 * @param {Object} params - 查询参数
 * @returns {Promise} - 评分列表
 */
export const getDoctorRatings = (doctorId, params = {}) => {
  const id = doctorId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供医生ID'));
  }
  return get(`/doctors/${id}/ratings`, params);
};

export default {
  getDoctorInfo,
  updateDoctorInfo,
  getDoctorAvailableTimes,
  addDoctorAvailableTime,
  deleteDoctorAvailableTime,
  getDoctorAppointments,
  getDoctorRatings
}; 