/**
 * 患者相关API
 */
import { get, put } from '@/utils/request';
import { getUserId } from '@/utils/storage';
// 导入模拟数据（仅在开发环境使用）
import { 
  mockGetPatientInfo, 
  mockGetRecommendedDoctors, 
  mockGetPatientAppointments,
  mockUpdatePatientInfo
} from '@/mock/patient';

// 是否使用模拟数据（开发环境且启用了模拟数据开关）
const USE_MOCK = process.env.NODE_ENV === 'development' && true;

/**
 * 获取患者个人信息
 * @param {string} patientId - 患者ID，不传则获取当前登录患者
 * @returns {Promise} - 患者信息
 */
export const getPatientInfo = (patientId) => {
  const id = patientId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供患者ID'));
  }
  
  if (USE_MOCK) {
    return mockGetPatientInfo(id);
  }
  return get(`/patients/${id}`);
};

/**
 * 更新患者个人信息
 * @param {string} patientId - 患者ID，不传则更新当前登录患者
 * @param {Object} data - 患者信息
 * @returns {Promise} - 更新结果
 */
export const updatePatientInfo = (patientId, data) => {
  const id = patientId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供患者ID'));
  }
  
  if (USE_MOCK) {
    return mockUpdatePatientInfo(id, data);
  }
  return put(`/patients/${id}`, data);
};

/**
 * 获取推荐医生列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 推荐医生列表
 */
export const getRecommendedDoctors = (params) => {
  if (USE_MOCK) {
    return mockGetRecommendedDoctors(params);
  }
  return get('/patients/recommended-doctors', params);
};

/**
 * 获取患者预约列表
 * @param {string} patientId - 患者ID，不传则获取当前登录患者
 * @param {Object} params - 查询参数
 * @returns {Promise} - 预约列表
 */
export const getPatientAppointments = (patientId, params = {}) => {
  const id = patientId || getUserId();
  if (!id) {
    return Promise.reject(new Error('未提供患者ID'));
  }
  
  if (USE_MOCK) {
    return mockGetPatientAppointments(id, params);
  }
  return get(`/patients/${id}/appointments`, params);
};

export default {
  getPatientInfo,
  updatePatientInfo,
  getRecommendedDoctors,
  getPatientAppointments
}; 