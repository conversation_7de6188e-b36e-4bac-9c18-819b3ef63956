<template>
  <view class="content">
    <image class="logo" src="/static/logo.png"></image>
    <view class="text-area">
      <text class="title">智慧口腔健康医疗</text>
      <text class="subtitle">正在跳转到登录页面...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '智慧口腔健康医疗',
    }
  },
  onLoad() {
    // 自动跳转到登录页面
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/index'
      });
    }, 1000); // 延迟1秒后跳转
  },
  methods: {}
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
</style>
