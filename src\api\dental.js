import request from '@/utils/request'

// 获取最近的口腔健康记录
export function getDentalRecords() {
  return request({
    url: 'http://175.178.241.219/api/get_dental_records/',
    method: 'get',
    timeout: 10000, // 增加超时时间到10秒
    retry: 2, // 失败后重试2次
    retryDelay: 1000 // 重试间隔1秒
  })
}

// 获取最近一次的口腔健康记录
export function getLatestDentalRecord() {
  return request({
    url: 'http://175.178.241.219/api/get_dental_records/',
    method: 'get',
    timeout: 10000, // 增加超时时间到10秒
    retry: 2, // 失败后重试2次
    retryDelay: 1000, // 重试间隔1秒
    transformResponse: [function (data) {
      try {
        const res = JSON.parse(data);
        if (res.success && res.records && res.records.length > 0) {
          // 返回最新的一条记录
          return {
            success: true,
            record: res.records[0]
          };
        }
        return res;
      } catch (e) {
        return {
          success: false,
          error: e.message
        };
      }
    }]
  })
} 

// 添加默认导出
export default {
  getDentalRecords,
  getLatestDentalRecord
} 