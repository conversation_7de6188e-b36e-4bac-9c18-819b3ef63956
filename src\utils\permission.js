/**
 * 路由权限控制
 */
import { isLoggedIn, isAdmin, isDoctor, isPatient, isTokenExpired } from './storage';

// 需要登录才能访问的页面
const authPages = [
  // 用户页面
  '/pages/home/<USER>',
  '/pages/doctor-selection/index',
  '/pages/appointment-detail/index',
  '/pages/appointment-records/index',
  '/pages/appointment-detail-view/index',
  '/pages/patient-dashboard/index',
  
  // 医生页面
  '/pages/doctor-dashboard/index',
  '/pages/doctor-profile/index',
  '/pages/schedule-management/index',
  '/pages/appointment-notifications/index',
  
  // 管理员页面
  '/pages/admin-dashboard/index',
  '/pages/doctor-management/index',
  '/pages/patient-management/index',
  '/pages/doctor-form/index',
  '/pages/patient-form/index'
];

// 仅用户可访问的页面
const patientOnlyPages = [
  '/pages/home/<USER>',
  '/pages/doctor-selection/index',
  '/pages/appointment-detail/index',
  '/pages/patient-dashboard/index'
];

// 仅医生可访问的页面
const doctorOnlyPages = [
  '/pages/doctor-dashboard/index',
  '/pages/schedule-management/index',
  '/pages/appointment-notifications/index'
];

// 仅管理员可访问的页面
const adminOnlyPages = [
  '/pages/admin-dashboard/index',
  '/pages/doctor-management/index',
  '/pages/patient-management/index',
  '/pages/doctor-form/index',
  '/pages/patient-form/index'
];

/**
 * 检查页面访问权限
 * @param {string} url - 页面路径
 * @returns {boolean} 是否有权限访问
 */
export const checkPermission = (url) => {
  // 提取路径，移除查询参数
  const path = url.split('?')[0];
  
  // 如果不需要认证，直接返回true
  if (!authPages.includes(path)) {
    return true;
  }
  
  // 检查登录状态
  if (!isLoggedIn() || isTokenExpired()) {
    return false;
  }
  
  // 检查用户专属页面
  if (patientOnlyPages.includes(path) && !isPatient()) {
    return false;
  }
  
  // 检查医生专属页面
  if (doctorOnlyPages.includes(path) && !isDoctor()) {
    return false;
  }
  
  // 检查管理员专属页面
  if (adminOnlyPages.includes(path) && !isAdmin()) {
    return false;
  }
  
  return true;
};

/**
 * 初始化权限控制
 */
export const setupPermissionGuard = () => {
  // 监听页面跳转
  uni.addInterceptor('navigateTo', {
    invoke(params) {
      if (!checkPermission(params.url)) {
        uni.showToast({
          title: '请先登录或无权限访问',
          icon: 'none'
        });
        uni.navigateTo({
          url: '/pages/login/index'
        });
        return false;
      }
      return params;
    }
  });
  
  uni.addInterceptor('redirectTo', {
    invoke(params) {
      if (!checkPermission(params.url)) {
        uni.showToast({
          title: '请先登录或无权限访问',
          icon: 'none'
        });
        uni.redirectTo({
          url: '/pages/login/index'
        });
        return false;
      }
      return params;
    }
  });
  
  uni.addInterceptor('reLaunch', {
    invoke(params) {
      if (!checkPermission(params.url)) {
        uni.showToast({
          title: '请先登录或无权限访问',
          icon: 'none'
        });
        uni.reLaunch({
          url: '/pages/login/index'
        });
        return false;
      }
      return params;
    }
  });
  
  uni.addInterceptor('switchTab', {
    invoke(params) {
      if (!checkPermission(params.url)) {
        uni.showToast({
          title: '请先登录或无权限访问',
          icon: 'none'
        });
        uni.reLaunch({
          url: '/pages/login/index'
        });
        return false;
      }
      return params;
    }
  });
  
  // 添加navigateBack拦截器，但不做任何拦截，让返回按钮正常工作
  uni.addInterceptor('navigateBack', {
    invoke(e) {
      // 不做任何拦截，让返回按钮正常工作
      return e;
    }
  });
};

export default {
  checkPermission,
  setupPermissionGuard
}; 