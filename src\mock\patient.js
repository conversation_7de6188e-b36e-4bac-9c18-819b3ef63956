/**
 * 患者相关的模拟数据
 */

// 模拟患者数据
const mockPatients = [
  {
    id: 'P10001',
    name: '张三',
    gender: '男',
    phone: '13800138000',
    email: '<EMAIL>',
    location: {
      latitude: 39.9042,
      longitude: 116.4074,
      address: '北京市朝阳区'
    }
  }
];

// 模拟医生数据
const mockDoctors = [
  {
    id: 'D10001',
    name: '李医生',
    gender: '男',
    specialization: '口腔正畸科',
    clinicLocation: {
      latitude: 39.9142,
      longitude: 116.4174,
      address: '北京市朝阳区健康路88号'
    },
    distance: 1.5,
    introduction: '从事口腔医学工作10年，擅长各类牙齿矫正和美容修复',
    rating: 4.8,
    ratingCount: 56
  },
  {
    id: 'D10002',
    name: '王医生',
    gender: '女',
    specialization: '口腔修复科',
    clinicLocation: {
      latitude: 39.9242,
      longitude: 116.4274,
      address: '北京市朝阳区阳光路66号'
    },
    distance: 2.3,
    introduction: '口腔修复专家，擅长各类牙齿修复和美容',
    rating: 4.9,
    ratingCount: 78
  },
  {
    id: 'D10003',
    name: '赵医生',
    gender: '男',
    specialization: '口腔内科',
    clinicLocation: {
      latitude: 39.9342,
      longitude: 116.4374,
      address: '北京市海淀区科技路99号'
    },
    distance: 3.8,
    introduction: '专注口腔内科疾病治疗，经验丰富',
    rating: 4.7,
    ratingCount: 45
  }
];

// 模拟预约数据
const mockAppointments = [
  {
    id: 'A10001',
    date: '2023-11-15',
    startTime: '09:00',
    endTime: '10:00',
    status: 'upcoming',
    doctor: {
      id: 'D10001',
      name: '李医生',
      specialization: '口腔正畸科',
      clinicAddress: '北京市朝阳区健康路88号'
    },
    description: '牙齿矫正咨询',
    createTime: '2023-11-01 10:00:00'
  },
  {
    id: 'A10002',
    date: '2023-10-20',
    startTime: '14:00',
    endTime: '15:00',
    status: 'completed',
    doctor: {
      id: 'D10002',
      name: '王医生',
      specialization: '口腔修复科',
      clinicAddress: '北京市朝阳区阳光路66号'
    },
    description: '蛀牙治疗',
    createTime: '2023-10-15 09:30:00'
  }
];

/**
 * 获取患者个人信息
 * @param {string} patientId - 患者ID
 * @returns {Promise} - 患者信息
 */
export const mockGetPatientInfo = (patientId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const patient = mockPatients.find(p => p.id === patientId);
      
      if (patient) {
        resolve({
          code: 200,
          message: '获取成功',
          data: patient
        });
      } else {
        reject({
          code: 404,
          message: '患者不存在'
        });
      }
    }, 500);
  });
};

/**
 * 更新患者个人信息
 * @param {string} patientId - 患者ID
 * @param {Object} data - 患者信息
 * @returns {Promise} - 更新结果
 */
export const mockUpdatePatientInfo = (patientId, data) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const patientIndex = mockPatients.findIndex(p => p.id === patientId);
      
      if (patientIndex !== -1) {
        // 更新患者信息
        mockPatients[patientIndex] = {
          ...mockPatients[patientIndex],
          ...data
        };
        
        resolve({
          code: 200,
          message: '更新成功',
          data: mockPatients[patientIndex]
        });
      } else {
        reject({
          code: 404,
          message: '患者不存在'
        });
      }
    }, 500);
  });
};

/**
 * 获取推荐医生列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 推荐医生列表
 */
export const mockGetRecommendedDoctors = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 根据专科筛选
      let filteredDoctors = [...mockDoctors];
      if (params.specialization) {
        filteredDoctors = filteredDoctors.filter(d => 
          d.specialization === params.specialization
        );
      }
      
      // 根据距离筛选
      if (params.maxDistance) {
        filteredDoctors = filteredDoctors.filter(d => 
          d.distance <= params.maxDistance
        );
      }
      
      // 分页处理
      const pageSize = params.pageSize || 10;
      const pageNum = params.pageNum || 1;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const pagedDoctors = filteredDoctors.slice(start, end);
      
      resolve({
        code: 200,
        message: '获取成功',
        data: {
          total: filteredDoctors.length,
          list: pagedDoctors
        }
      });
    }, 800);
  });
};

/**
 * 获取患者预约列表
 * @param {string} patientId - 患者ID
 * @param {Object} params - 查询参数
 * @returns {Promise} - 预约列表
 */
export const mockGetPatientAppointments = (patientId, params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 根据状态筛选
      let filteredAppointments = [...mockAppointments];
      if (params.status && params.status !== 'all') {
        filteredAppointments = filteredAppointments.filter(a => 
          a.status === params.status
        );
      }
      
      // 分页处理
      const pageSize = params.pageSize || 10;
      const pageNum = params.pageNum || 1;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const pagedAppointments = filteredAppointments.slice(start, end);
      
      resolve({
        code: 200,
        message: '获取成功',
        data: {
          total: filteredAppointments.length,
          list: pagedAppointments
        }
      });
    }, 600);
  });
};

export default {
  mockGetPatientInfo,
  mockUpdatePatientInfo,
  mockGetRecommendedDoctors,
  mockGetPatientAppointments
}; 