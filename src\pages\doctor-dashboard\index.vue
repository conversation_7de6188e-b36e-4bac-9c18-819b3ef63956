<template>
  <view class="container">
    <!-- 欢迎语区域 -->
    <view class="welcome-banner">
      <text class="welcome-text">欢迎使用智慧口腔健康医疗</text>
      <text class="welcome-user">尊敬的医生</text>
    </view>
    
    <view class="options-container">
      <!-- 管理开放时间选项 -->
      <view class="option-card" @tap="navigateToSchedule">
        <view class="option-content">
          <text class="option-title">管理开放时间</text>
          <text class="option-desc">设置您的出诊时间和可预约时段</text>
        </view>
        <view class="option-icon">
          <text class="icon">🕒</text>
        </view>
      </view>
      
      <!-- 接收门诊消息选项 -->
      <view class="option-card" @tap="navigateToNotifications">
        <view class="option-content">
          <text class="option-title">接收门诊消息</text>
          <text class="option-desc">查看患者预约和就诊提醒</text>
        </view>
        <view class="option-icon">
          <text class="icon">📨</text>
        </view>
      </view>
      
      <!-- 医生信息管理选项 -->
      <view class="option-card" @tap="navigateToProfile">
        <view class="option-content">
          <text class="option-title">医生信息管理</text>
          <text class="option-desc">更新个人资料和专业信息</text>
        </view>
        <view class="option-icon">
          <text class="icon">👨‍⚕️</text>
        </view>
      </view>
    </view>
    
    <!-- 退出登录按钮 -->
    <view class="logout-container">
      <button class="logout-button" @tap="logout">退出登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      doctorName: '医生'
    }
  },
  onLoad() {
    // 尝试获取医生信息
    try {
      const doctorInfo = uni.getStorageSync('doctorInfo');
      if (doctorInfo && doctorInfo.name) {
        this.doctorName = doctorInfo.name;
      }
    } catch (e) {
      console.error('获取医生信息失败', e);
    }
  },
  methods: {
    navigateToSchedule() {
      // 添加过渡效果
      uni.showLoading({
        title: '正在加载...',
        mask: true
      });
      
      // 跳转到排班管理页面
      uni.navigateTo({
        url: '/pages/schedule-management/index',
        success: () => {
          uni.hideLoading();
        },
        fail: (error) => {
          console.error('导航失败', error);
          uni.hideLoading();
          uni.showToast({
            title: '导航失败，请重试',
            icon: 'none'
          });
        }
      });
    },
    navigateToNotifications() {
      uni.navigateTo({
        url: '/pages/appointment-notifications/index'
      });
    },
    navigateToProfile() {
      uni.navigateTo({
        url: '/pages/doctor-profile/index'
      });
    },
    // 退出登录方法
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '您确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录状态和用户信息
            try {
              uni.removeStorageSync('doctorInfo');
              uni.removeStorageSync('token');
              // 可以根据需要清除其他存储的信息
              
              // 跳转到登录页面
              uni.reLaunch({
                url: '/pages/login/index'
              });
            } catch (e) {
              console.error('退出登录失败', e);
              uni.showToast({
                title: '退出失败，请重试',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

/* 欢迎语样式 */
.welcome-banner {
  padding: 40rpx 30rpx 30rpx; /* 减少内边距 */
  text-align: center;
  margin-bottom: 10rpx; /* 减少底部间距 */
}

.welcome-text {
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 10rpx;
  display: block;
}

.welcome-user {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  display: block;
}

.options-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center; /* 添加垂直居中 */
  align-items: center; /* 添加水平居中 */
  padding: 20rpx 30rpx; /* 减少内边距 */
  gap: 30rpx; /* 减少按钮间距 */
}

.option-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 35rpx 30rpx; /* 减少内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%; /* 确保卡片宽度一致 */
  max-width: 600rpx; /* 设置最大宽度，保持美观 */
}

.option-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.option-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: rgba(142, 153, 243, 0.2);
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  font-size: 50rpx;
}

/* 退出登录按钮样式 */
.logout-container {
  padding: 20rpx 30rpx; /* 减少内边距 */
  margin-top: -60rpx; /* 调整上移距离，确保一屏显示 */
}

.logout-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #5a67d8;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 50rpx;
  border: 1rpx solid rgba(90, 103, 216, 0.3);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.logout-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
</style> 