﻿<template>
  <view class="login-container">
    <!-- 登录卡片 -->
    <view class="login-card">
      <view class="login-header">
        <text class="title">欢迎回来</text>
        <text class="subtitle">请登录您的账号</text>
      </view>
      
      <!-- 模拟账号提示 -->
      <view class="mock-tips" v-if="showMockTips">
        <text class="mock-title">测试账号:</text>
        <text class="mock-account">患者: <EMAIL> / 123456</text>
        <text class="mock-account">医生: <EMAIL> / 123456</text>
        <text class="mock-account">管理员: <EMAIL> / 123456</text>
      </view>
      
      <view class="form-item">
        <text class="label">账号</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="text" 
            placeholder="请输入手机号或邮箱" 
            v-model="formData.account" 
            placeholder-class="placeholder"
          />
          <text class="icon"></text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">密码</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="password" 
            placeholder="请输入密码" 
            v-model="formData.password" 
            placeholder-class="placeholder"
          />
          <text class="icon"></text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">角色</text>
        <view class="role-selector">
          <view 
            class="role-option" 
            :class="{ active: formData.userType === 'patient' }"
            @tap="selectRole('patient')"
          >
            <text class="role-icon">👤</text>
            <text class="role-text">用户</text>
          </view>
          <view 
            class="role-option" 
            :class="{ active: formData.userType === 'doctor' }"
            @tap="selectRole('doctor')"
          >
            <text class="role-icon">👨‍⚕️</text>
            <text class="role-text">医生</text>
          </view>
          <view 
            class="role-option" 
            :class="{ active: formData.userType === 'admin' }"
            @tap="selectRole('admin')"
          >
            <text class="role-icon">🔐</text>
            <text class="role-text">管理员</text>
          </view>
        </view>
      </view>
      
      <view class="form-options">
        <view class="remember-me">
          <checkbox 
            :checked="formData.remember" 
            @tap="toggleRemember" 
            class="checkbox" 
            color="#a8c0ff"
          />
          <text>记住我</text>
        </view>
        <text class="forgot-password" @tap="forgotPassword">忘记密码?</text>
      </view>
      
      <button class="login-button" @tap="handleLogin" :disabled="loading">
        <text v-if="loading">登录中...</text>
        <text v-else>登 录</text>
      </button>
      
      <view class="signup-link">
        <text>还没有账号？</text>
        <text class="link" @tap="goToRegister">立即注册</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { setToken } from '@/utils/request';
import { saveUserInfo } from '@/utils/storage';

// 获取全局API
const { proxy } = getCurrentInstance();
const { $api } = proxy;

// 加载状态
const loading = ref(false);

// 表单数据
const formData = ref({
  account: "",
  password: "",
  remember: false,
  userType: "patient" // 默认为用户角色
});

// 模拟账号提示显示状态
const showMockTips = ref(false);

// 页面加载时接收参数
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options;
  
  console.log('登录页面接收参数:', options);
  
  // 如果有account参数，自动填充到表单
  if (options && options.account) {
    formData.value.account = options.account;
    uni.showToast({
      title: '注册成功，请登录',
      icon: 'none'
    });
  }
});

// 选择角色
const selectRole = (role) => {
  formData.value.userType = role;
};

// 切换记住我
const toggleRemember = () => {
  formData.value.remember = !formData.value.remember;
};

// 登录处理
const handleLogin = async () => {
  // 表单验证
  if (!formData.value.account) {
    uni.showToast({
      title: "请输入手机号或邮箱",
      icon: "none"
    });
    return;
  }
  
  if (!formData.value.password) {
    uni.showToast({
      title: "请输入密码",
      icon: "none"
    });
    return;
  }
  
  // 设置加载状态
  loading.value = true;
  
  try {
    // 准备登录数据，确保字段名称与API文档一致
    const loginData = {
      account: formData.value.account,  // 手机号或邮箱
      password: formData.value.password, // 密码
      userType: formData.value.userType  // 用户类型
    };
    
    console.log('准备登录数据:', JSON.stringify(loginData, null, 2));
    
    // 调用登录API
    console.log('开始调用登录API...');
    const result = await $api.auth.login(loginData);
    console.log('登录API调用成功:', JSON.stringify(result, null, 2));
    
    // 登录成功
    uni.showToast({
      title: "登录成功",
      icon: "success"
    });
    
    // 根据用户类型跳转到不同页面
    setTimeout(() => {
      if (formData.value.userType === 'patient') {
        // 跳转到用户中心
        uni.redirectTo({
          url: "/pages/patient-dashboard/index"
        });
      } else if (formData.value.userType === 'doctor') {
        // 跳转到医生工作台
        uni.redirectTo({
          url: "/pages/doctor-dashboard/index"
        });
      } else if (formData.value.userType === 'admin') {
        // 跳转到管理员控制台
        uni.redirectTo({
          url: "/pages/admin-dashboard/index"
        });
      }
    }, 1500);
    
  } catch (error) {
    // 登录失败
    console.error('登录失败:', JSON.stringify(error, null, 2));
    
    // 显示具体的错误信息
    let errorMessage = "登录失败，请检查账号和密码";
    
    if (error.message) {
      errorMessage = error.message;
      
      // 如果是用户类型不匹配的错误，高亮显示对应的用户类型选项
      if (error.message.includes('患者类型')) {
        formData.value.userType = 'patient';
      } else if (error.message.includes('医生类型')) {
        formData.value.userType = 'doctor';
      } else if (error.message.includes('管理员类型')) {
        formData.value.userType = 'admin';
      }
    } else if (error.errMsg && error.errMsg.includes("request:fail")) {
      errorMessage = "网络连接失败，请检查网络设置";
    } else if (error.code === 500) {
      errorMessage = "服务器内部错误，已启用本地模拟数据";
      // 提示用户使用模拟账号
      console.log('建议使用模拟账号登录：');
      console.log('患者账号: <EMAIL> 密码: 123456');
      console.log('医生账号: <EMAIL> 密码: 123456');
      console.log('管理员账号: <EMAIL> 密码: 123456');
      showMockTips.value = true; // 显示模拟账号提示
    }
    
    uni.showToast({
      title: errorMessage,
      icon: "none",
      duration: 3000
    });
  } finally {
    // 重置加载状态
    loading.value = false;
  }
};

// 忘记密码
const forgotPassword = () => {
  uni.navigateTo({
    url: "/pages/forgot-password/index"
  });
};

// 去注册页面
const goToRegister = () => {
  uni.navigateTo({
    url: "/pages/register/index"
  });
};
</script>

<style lang="scss">
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #a8c0ff;
  padding: 30rpx;
}

.login-card {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.login-header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 44rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.form-item {
  margin-bottom: 30rpx;
  
  .label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
    display: block;
  }
  
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    
    .input {
      flex: 1;
      height: 90rpx;
      border: 1px solid #e0e0e0;
      border-radius: 16rpx;
      padding: 0 80rpx 0 30rpx;
      font-size: 28rpx;
      background-color: #fff;
      
      &:focus {
        border-color: #a8c0ff;
      }
    }
    
    .icon {
      position: absolute;
      right: 30rpx;
      font-size: 36rpx;
      color: #999;
    }
    
    .placeholder {
      color: #999;
    }
  }
}

.role-selector {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.role-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  background-color: #fff;
  transition: all 0.3s;
  
  &.active {
    border-color: #a8c0ff;
    background-color: rgba(168, 192, 255, 0.1);
  }
}

.role-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.role-text {
  font-size: 26rpx;
  color: #333;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  font-size: 26rpx;
  
  .remember-me {
    display: flex;
    align-items: center;
    
    .checkbox {
      transform: scale(0.8);
      margin-right: 8rpx;
    }
    
    text {
      color: #666;
    }
  }
  
  .forgot-password {
    color: #8e99f3;
  }
}

.login-button {
  width: 100%;
  height: 90rpx;
  background-color: #a8c0ff;
  color: #fff;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  
  &:active {
    background-color: #97b1f8;
  }
  
  &[disabled] {
    background-color: #d0d0d0;
    color: #f0f0f0;
  }
}

.signup-link {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  
  .link {
    color: #8e99f3;
    margin-left: 10rpx;
  }
}

.mock-tips {
  background-color: #f0f0f0;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  text-align: left;
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;

  .mock-title {
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .mock-account {
    color: #8e99f3;
    margin-left: 20rpx;
  }
}
</style>
 