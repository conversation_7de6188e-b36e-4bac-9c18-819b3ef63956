<template>
  <view class="container">
    <!-- 健康状况摘要 -->
    <view class="section" v-if="dentalRecord">
      <view class="section-title">您的口腔健康状况</view>
      <view class="health-summary">
        <view class="health-score">
          <view class="score-circle" :class="getScoreClass(dentalRecord.health_score)">
            <text class="score-text">{{ dentalRecord.health_score }}</text>
          </view>
          <text class="score-label">健康评分</text>
        </view>
        <view class="health-desc">
          <text class="desc-text">{{ getHealthDescription(dentalRecord.health_score) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-section" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在为您生成个性化健康建议...</text>
    </view>
    
    <!-- 护理建议列表 -->
    <block v-if="!loading">
      <view class="section" v-if="careRecommendations.length > 0">
        <view class="section-title">为您推荐的健康建议</view>
        <view class="recommendation-reason">
          <text>{{ getRecommendationReason() }}</text>
        </view>

        <view class="care-list">
          <view
            class="care-card"
            v-for="(care, index) in careRecommendations"
            :key="index"
          >
            <view class="care-icon">
              <text class="icon-text">{{ care.icon }}</text>
            </view>
            <view class="care-content">
              <view class="care-header">
                <text class="care-title">{{ care.title }}</text>
                <view class="priority-badge" :class="care.priority">
                  <text class="priority-text">{{ care.priorityText }}</text>
                </view>
              </view>
              <text class="care-description">{{ care.description }}</text>
              <view class="care-suggestions">
                <view class="suggestion-item" v-for="(suggestion, idx) in care.suggestions" :key="idx">
                  <text class="suggestion-text">• {{ suggestion }}</text>
                </view>
              </view>
              <view class="care-note" v-if="care.note">
                <text class="note-text">💡 {{ care.note }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无推荐时显示 -->
      <view class="section no-data" v-else>
        <text class="no-data-text">暂无健康建议</text>
        <text class="no-data-desc">请完善您的口腔健康数据，以便我们为您提供个性化健康建议</text>
      </view>
    </block>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="primary-btn full-width" @tap="refreshRecommendations">刷新健康建议</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { dentalApi } from '@/api';
import dentalMock from '@/mock/dental';



// 口腔健康记录
const dentalRecord = ref(null);
const loading = ref(true);
const careRecommendations = ref([]);

// 获取口腔健康记录
const fetchDentalRecord = async () => {
  try {
    loading.value = true;
    
    // 使用实际API接口
    const res = await dentalApi.getLatestDentalRecord();
    if (res.success && res.record) {
      dentalRecord.value = res.record;
      generateCareRecommendations();
    } else {
      console.error('获取口腔健康记录失败', res);
      // 使用模拟数据
      useMockData();
    }
  } catch (error) {
    console.error('获取口腔健康记录错误', error);
    // 使用模拟数据
    useMockData();
  } finally {
    loading.value = false;
  }
};

// 使用模拟数据
const useMockData = () => {
  console.log('使用模拟数据');
  const res = dentalMock.getLatestDentalRecord();
  if (res.success && res.record) {
    dentalRecord.value = res.record;
    generateCareRecommendations();
  } else {
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
    loading.value = false;
  }
};

// 根据健康状况生成护理建议
const generateCareRecommendations = () => {
  if (!dentalRecord.value) {
    careRecommendations.value = [];
    return;
  }

  const score = dentalRecord.value.health_score;
  const plaque = parseFloat((dentalRecord.value.plaque_ratio || '0%').replace('%', ''));
  const caries = parseFloat((dentalRecord.value.caries_ratio || '0%').replace('%', ''));
  const cavity = parseFloat((dentalRecord.value.cavity_ratio || '0%').replace('%', ''));

  // 护理建议数据
  const allCareAdvice = [
    {
      id: 1,
      title: '牙菌斑清洁护理',
      icon: '🦷',
      priority: 'high',
      priorityText: '重要',
      description: '您的牙菌斑比例较高，需要加强清洁护理',
      suggestions: [
        '选择含抑菌成分的牙膏，如含三氯生或氯己定的产品',
        '使用抗菌漱口水，每日早晚各一次',
        '考虑使用电动牙刷，清洁效果更佳',
        '增加牙线使用频率，清洁牙缝残留'
      ],
      note: '建议每3个月更换牙刷，保持清洁工具卫生',
      forPlaque: true,
      forGeneral: true
    },
    {
      id: 2,
      title: '龋齿预防护理',
      icon: '🛡️',
      priority: 'high',
      priorityText: '重要',
      description: '检测到龋齿风险，需要重点预防护理',
      suggestions: [
        '使用含氟牙膏，有效预防蛀牙',
        '减少糖分摄入，特别是粘性甜食',
        '餐后及时漱口，清除食物残渣',
        '定期使用含氟漱口水加强保护'
      ],
      note: '建议每6个月进行一次专业洁牙和检查',
      forCaries: true,
      forGeneral: true
    },
    {
      id: 3,
      title: '敏感牙齿护理',
      icon: '❄️',
      priority: 'medium',
      priorityText: '建议',
      description: '针对牙齿敏感问题的专业护理建议',
      suggestions: [
        '选择专业抗敏感牙膏，含硝酸钾成分',
        '使用软毛牙刷，避免过度刷牙',
        '避免过冷过热食物刺激',
        '考虑使用脱敏漱口水辅助治疗'
      ],
      note: '如敏感症状持续，建议及时就医检查',
      forCavity: true,
      forGeneral: false
    },
    {
      id: 4,
      title: '牙龈健康护理',
      icon: '💗',
      priority: 'medium',
      priorityText: '建议',
      description: '维护牙龈健康，预防牙龈疾病',
      suggestions: [
        '选择软毛牙刷，温和清洁牙龈线',
        '使用牙龈护理专用牙膏',
        '定期使用牙线清洁牙缝',
        '适当按摩牙龈，促进血液循环'
      ],
      note: '如出现牙龈出血或肿痛，请及时就医',
      forPlaque: true,
      forGeneral: true
    },
    {
      id: 5,
      title: '美白护理建议',
      icon: '✨',
      priority: 'low',
      priorityText: '可选',
      description: '安全有效的牙齿美白护理方案',
      suggestions: [
        '选择温和美白牙膏，避免过度研磨',
        '减少咖啡、茶、红酒等着色食物',
        '使用美白漱口水辅助护理',
        '考虑专业美白治疗，效果更佳'
      ],
      note: '美白过程中如有不适，应立即停止使用',
      forGeneral: true,
      forPlaque: false
    },
    {
      id: 6,
      title: '深度清洁护理',
      icon: '🌊',
      priority: 'medium',
      priorityText: '建议',
      description: '全面深度清洁，维护口腔健康',
      suggestions: [
        '使用冲牙器深度清洁牙缝',
        '定期使用牙间刷清洁难达区域',
        '选择多功能电动牙刷',
        '配合使用舌苔清洁器'
      ],
      note: '深度清洁应循序渐进，避免过度刺激',
      forPlaque: true,
      forCaries: true,
      forCavity: true
    },
    {
      id: 7,
      title: '日常维护护理',
      icon: '📅',
      priority: 'medium',
      priorityText: '建议',
      description: '建立良好的日常口腔护理习惯',
      suggestions: [
        '每日早晚刷牙，每次至少2分钟',
        '饭后漱口，保持口腔清洁',
        '定期更换牙刷，保持卫生',
        '均衡饮食，减少糖分摄入'
      ],
      note: '良好的习惯是口腔健康的基础',
      forGeneral: true,
      forPlaque: true,
      forCaries: true
    }
  ];
  
  // 根据健康状况筛选护理建议
  let filtered = [];

  if (score < 70) {
    // 健康状况较差，需要全面护理
    filtered = allCareAdvice.filter(p => p.forGeneral);
  }

  if (plaque > 10) {
    // 牙菌斑比例高，推荐清洁护理
    const plaqueAdvice = allCareAdvice.filter(p => p.forPlaque);
    filtered = [...new Set([...filtered, ...plaqueAdvice])];
  }

  if (caries > 5) {
    // 龋齿比例高，推荐防蛀护理
    const cariesAdvice = allCareAdvice.filter(p => p.forCaries);
    filtered = [...new Set([...filtered, ...cariesAdvice])];
  }

  if (cavity > 3) {
    // 有牙洞，推荐敏感牙护理
    const cavityAdvice = allCareAdvice.filter(p => p.forCavity);
    filtered = [...new Set([...filtered, ...cavityAdvice])];
  }

  // 如果没有筛选出护理建议，则推荐通用护理
  if (filtered.length === 0) {
    filtered = allCareAdvice.filter(p => p.forGeneral);
  }

  // 随机打乱护理建议顺序，增加推荐多样性
  filtered = shuffleArray(filtered);

  // 最多显示5个护理建议
  careRecommendations.value = filtered.slice(0, 5);
};

// 随机打乱数组
const shuffleArray = (array) => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

// 刷新推荐
const refreshRecommendations = () => {
  fetchDentalRecord();
  uni.showToast({
    title: '健康建议已刷新',
    icon: 'success',
    duration: 1500
  });
};



// 获取健康评分等级样式
const getScoreClass = (score) => {
  if (score >= 85) return 'score-good';
  if (score >= 70) return 'score-medium';
  return 'score-bad';
};

// 获取健康描述
const getHealthDescription = (score) => {
  if (score >= 85) return '您的口腔健康状况良好，建议继续保持良好的口腔卫生习惯。';
  if (score >= 70) return '您的口腔健康状况一般，需要加强口腔护理，定期检查。';
  return '您的口腔健康状况需要改善，建议加强日常护理并尽快就医。';
};

// 获取推荐理由
const getRecommendationReason = () => {
  if (!dentalRecord.value) return '';

  const score = dentalRecord.value.health_score;
  const plaque = parseFloat((dentalRecord.value.plaque_ratio || '0%').replace('%', ''));
  const caries = parseFloat((dentalRecord.value.caries_ratio || '0%').replace('%', ''));

  let reason = '根据您的口腔健康数据，我们为您提供以下健康建议：';

  if (score < 70) {
    reason += '您的口腔健康状况需要改善，建议加强日常护理；';
  }

  if (plaque > 10) {
    reason += '您的牙菌斑比例较高，建议重点清洁护理；';
  }

  if (caries > 5) {
    reason += '您有龋齿风险，建议加强预防护理；';
  }

  return reason;
};

// 页面加载时
onMounted(() => {
  fetchDentalRecord();
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
}



.section {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #4a90e2;
  padding-left: 15rpx;
}

.health-summary {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.health-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}

.score-good {
  background-color: #4cd964;
}

.score-medium {
  background-color: #ffcc00;
}

.score-bad {
  background-color: #ff3b30;
}

.score-text {
  font-size: 40rpx;
  font-weight: 600;
  color: #ffffff;
}

.score-label {
  font-size: 24rpx;
  color: #666;
}

.health-desc {
  flex: 1;
}

.desc-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(74, 144, 226, 0.3);
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.recommendation-reason {
  margin-bottom: 30rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  border-left: 4rpx solid #4a90e2;
}

.care-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.care-card {
  display: flex;
  background-color: #fff;
  border-radius: 15rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid #4a90e2;
}

.care-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f7ff;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.icon-text {
  font-size: 36rpx;
}

.care-content {
  flex: 1;
}

.care-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.care-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.priority-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.priority-badge.high {
  background-color: #ffe6e6;
  color: #ff4757;
}

.priority-badge.medium {
  background-color: #fff3cd;
  color: #856404;
}

.priority-badge.low {
  background-color: #e6f7ff;
  color: #1890ff;
}

.priority-text {
  font-size: 20rpx;
  font-weight: 500;
}

.care-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.care-suggestions {
  margin-bottom: 15rpx;
}

.suggestion-item {
  margin-bottom: 8rpx;
}

.suggestion-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.4;
}

.care-note {
  background-color: #f8f9fa;
  padding: 12rpx 15rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #17a2b8;
}

.note-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.no-data-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.no-data-desc {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

.footer-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
  padding: 0 20rpx;
}

.primary-btn {
  height: 90rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.full-width {
  width: 100%;
}

.primary-btn:active {
  transform: scale(0.98);
}
</style> 