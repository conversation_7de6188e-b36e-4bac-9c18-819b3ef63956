<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-info">
      <view class="phone-number">{{ userInfo.phone || '暂无电话号码' }}</view>
      <view class="welcome-text">尊敬的用户您好</view>
    </view>
    
    <!-- 预约记录标题 -->
    <view class="records-title">预约记录</view>
    
    <!-- 预约记录列表 -->
    <scroll-view class="records-list" scroll-y>
      <view 
        v-for="(record, index) in appointmentRecords" 
        :key="index"
        class="record-card"
        @tap="viewAppointmentDetail(record)"
      >
        <view class="record-item">
          <view class="record-label">预约时间</view>
          <view class="record-value time-value">{{ record.appointmentTime }}</view>
        </view>
        <view class="record-item">
          <view class="record-label">专科</view>
          <view class="record-value">{{ record.specialty }}</view>
        </view>
        <view class="record-item">
          <view class="record-label">就诊医生</view>
          <view class="record-value">{{ record.doctorName }}</view>
        </view>
        <view class="record-item">
          <view class="record-label">诊所地址</view>
          <view class="record-value">{{ record.clinicAddress }}</view>
        </view>
        <view class="record-status" :class="getStatusClass(record.status)">
          {{ getStatusText(record.status) }}
        </view>
      </view>
      
      <view v-if="appointmentRecords.length === 0" class="empty-records">
        <view class="empty-text">暂无预约记录</view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="btn-section">
      <button class="return-btn" @tap="returnToDashboard">返回用户中心</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 用户信息
const userInfo = ref({
  phone: '',
  name: ''
});

// 预约记录
const appointmentRecords = ref([
  {
    id: '1001',
    appointmentTime: '2025-07-10 09:00-10:00',
    specialty: '正畸专科',
    doctorName: '张医生',
    clinicAddress: '北京市海淀区中关村大街1号医疗中心',
    status: 'upcoming'
  },
  {
    id: '1002',
    appointmentTime: '2025-07-05 14:30-15:30',
    specialty: '牙周专科',
    doctorName: '李医生',
    clinicAddress: '北京市西城区西单北大街131号',
    status: 'completed'
  },
  {
    id: '1003',
    appointmentTime: '2025-06-28 10:00-11:00',
    specialty: '儿童口腔',
    doctorName: '周医生',
    clinicAddress: '北京市东城区东直门外大街48号东方银座',
    status: 'cancelled'
  }
]);

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'upcoming':
      return 'status-upcoming';
    case 'completed':
      return 'status-completed';
    case 'cancelled':
      return 'status-cancelled';
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'upcoming':
      return '即将就诊';
    case 'completed':
      return '已完成';
    case 'cancelled':
      return '已取消';
    default:
      return '未知状态';
  }
};

// 按时间排序的预约记录
const sortedRecords = computed(() => {
  return [...appointmentRecords.value].sort((a, b) => {
    return new Date(a.appointmentTime.split(' ')[0]) - new Date(b.appointmentTime.split(' ')[0]);
  });
});

// 返回用户中心
const returnToDashboard = () => {
  uni.navigateBack();
};

// 查看预约详情
const viewAppointmentDetail = (record) => {
  // 保存当前选中的预约ID
  uni.setStorageSync('currentAppointmentId', record.id);
  
  // 跳转到预约详情页面
  uni.navigateTo({
    url: '/pages/appointment-detail-view/index',
    success: (res) => {
      // 通过eventChannel向打开页面传送数据
      res.eventChannel.emit('appointmentDetail', record);
    }
  });
};

// 页面加载时
onMounted(() => {
  // 获取用户信息
  try {
    const user = uni.getStorageSync('userInfo');
    if (user) {
      userInfo.value = user;
    }
  } catch (e) {
    console.error('获取用户信息失败', e);
  }
  
  // 获取预约信息（实际项目中应从API获取）
  try {
    const appointmentInfo = uni.getStorageSync('appointmentInfo');
    if (appointmentInfo) {
      // 在实际项目中，这里应该是API调用获取预约记录
      console.log('获取到预约信息:', appointmentInfo);
    }
  } catch (e) {
    console.error('获取预约信息失败', e);
  }
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

.user-info {
  margin-bottom: 30rpx;
  text-align: center;
}

.phone-number {
  font-size: 32rpx;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  margin-bottom: 20rpx;
  display: inline-block;
}

.welcome-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.records-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.records-list {
  flex: 1;
  width: 100%;
  margin-bottom: 30rpx;
}

.record-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.record-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.record-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.record-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

.time-value {
  color: #4a90e2;
  font-weight: 600;
}

.record-status {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-upcoming {
  background-color: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  border: 1rpx solid #4a90e2;
}

.status-completed {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1rpx solid #4caf50;
}

.status-cancelled {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1rpx solid #f44336;
}

.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.btn-section {
  margin-top: 20rpx;
}

.return-btn {
  width: 100%;
  height: 90rpx;
  background-color: #8e99f3;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(142, 153, 243, 0.3);
  
  &:active {
    background-color: #7a87e6;
    transform: scale(0.98);
  }
}
</style> 