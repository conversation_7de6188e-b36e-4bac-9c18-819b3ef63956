import json
import base64
from datetime import datetime

# 伪代码模型类，请根据实际项目替换
class DentalResult:
    """牙科检查结果模型类（伪代码）"""
    class Manager:
        def all(self):
            return self
        
        def order_by(self, field):
            return self
        
        def __getitem__(self, key):
            # 模拟返回7条记录
            return [
                DentalResult(
                    id=i,
                    created_at=datetime.now(),
                    health_score=95 - i*5,
                    caries_ratio=0.05 + i*0.01,
                    cavity_ratio=0.03 + i*0.01,
                    total_decay_ratio=0.08 + i*0.02,
                    plaque_ratio=0.1 + i*0.05,
                    cleanliness_score=90 - i*3,
                    processed_images='[]',
                    image=None
                ) for i in range(1, 8)
            ]
    
    objects = Manager()
    
    def __init__(self, id=None, created_at=None, health_score=None, 
                 caries_ratio=None, cavity_ratio=None, total_decay_ratio=None, 
                 plaque_ratio=None, cleanliness_score=None, processed_images=None, image=None):
        self.id = id
        self.created_at = created_at or datetime.now()  # 确保created_at永远不为None
        self.health_score = health_score
        self.caries_ratio = caries_ratio
        self.cavity_ratio = cavity_ratio
        self.total_decay_ratio = total_decay_ratio
        self.plaque_ratio = plaque_ratio
        self.cleanliness_score = cleanliness_score
        self.processed_images = processed_images
        self.image = image
    
    class Image:
        def __init__(self, url=None):
            self.url = url

# 伪代码响应类，请根据实际项目替换
class JsonResponse:
    def __init__(self, data, status=200, **kwargs):
        self.data = data
        self.status = status
        # 在实际项目中，这个类应该返回JSON响应

def is_valid_base64(s):
    """检查字符串是否是有效的Base64编码"""
    try:
        # 检查字符串是否符合Base64格式
        if not isinstance(s, str):
            return False
        
        # 移除可能的前缀
        if s.startswith('data:image'):
            # 处理 data URI scheme 格式
            s = s.split(',', 1)[1] if ',' in s else s
            
        base64.b64decode(s)
        return True
    except Exception:
        return False

def get_dental_records(request):
    try:
        # 获取最近7条记录
        records = DentalResult.objects.all().order_by('-created_at')[:7]
        records_data = []

        for record in records:
            # 处理 processed_images 字段
            processed_images = []
            if record.processed_images:
                try:
                    # 尝试解析 JSON 字符串
                    image_list = json.loads(record.processed_images)
                    if isinstance(image_list, list):
                        # 验证每个 Base64 编码的图像
                        for img in image_list:
                            if isinstance(img, str) and is_valid_base64(img):
                                processed_images.append(img)
                            else:
                                print(f"Invalid or non-string Base64 image data in record {record.id}: {img}")
                except json.JSONDecodeError:
                    # 记录 JSON 解析错误
                    print(f"Failed to decode processed_images for record {record.id}: {record.processed_images}")

            # 构建记录数据
            record_data = {
                'id': record.id,
                'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'health_score': record.health_score,
                'caries_ratio': f"{record.caries_ratio:.2%}" if record.caries_ratio is not None else "0%",
                'cavity_ratio': f"{record.cavity_ratio:.2%}" if record.cavity_ratio is not None else "0%",
                'total_decay_ratio': f"{record.total_decay_ratio:.2%}" if record.total_decay_ratio is not None else "0%",
                'plaque_ratio': f"{record.plaque_ratio:.2%}" if record.plaque_ratio is not None else "0%",
                'cleanliness_score': record.cleanliness_score,
                'img': record.image.url if record.image else None,  # 添加图片字段
                'processed_images': processed_images  # 处理后的图像字段（仅包含有效的 Base64 数据）
            }

            records_data.append(record_data)

        return JsonResponse({'success': True, 'records': records_data})

    except Exception as e:
        print(f"Error in get_dental_records: {str(e)}")  
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
